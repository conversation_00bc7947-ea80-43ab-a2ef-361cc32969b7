.statistical-index {
  width: 100%;
  height: 100%;
  padding: 8px;
  background-color: #f0f2f5;

  :global {
    .ant-spin-nested-loading {
      width: 100%;
      height: 100%;
    }

    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}

.statistical-index-header {
  width: 100%;
  height: 60px;
  background-color: #fff;
  margin-bottom: 8px;
}

.statistical-index-header-title {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.statistical-index-content {
  width: 100%;
  height: 100%;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  padding: 10px;
}

.statistical-index-content-left {
  width: 40%;
  height: auto;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(0 0 0 / 10%);
  padding: 8px;
}

.left-header {
  width: 100%;
  padding: 10px;
}

.statistical-index-content-right {
  width: 60%;
  height: auto;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgb(0 0 0 / 10%);
  padding: 8px;
}

.air-quality-card {
  width: 100%;
  overflow: hidden;
  border: 1px solid #f2efef;
  padding: 10px;
}

.air-quality-card-date {
  margin-bottom: 15px;
}

.air-quality-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.air-quality-label {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #2e8b57;
  color: #fff;
  padding: 6px 10px;
  font-size: 12px;
  z-index: 1;
  overflow: hidden;
}

.air-quality-label::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(to right, #1cd94a, #106f23);
  z-index: -1;
}

.air-quality-label::after {
  content: '';
  position: absolute;
  inset: 2px;
  background: linear-gradient(to right, #5b8264, #045d1f);
  z-index: -1;
}

.leaf-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  background-image: url('~@/assets/leaf.png');
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
}

.air-quality-level {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  padding-left: 15px;
  position: relative;
}

.quality-right {
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-around;
}

.quality-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qualitys-value {
  font-size: 16px;
  font-weight: 600;
  color: #5096c0;
}

.quality-label {
  font-size: 12px;
  color: #333;
}

.aqi-indicator {
  margin: 15px 0;
}

.indicator-bar {
  position: relative;
  width: 100%;
  height: 16px;
  background: linear-gradient(to right, #0f0, #ff0, #ffa500, #f00, #9a014d, #7d0124);
  border-radius: 8px;
  margin-bottom: 15px;
}

.indicator-mask {
  position: absolute;
  top: 0;
  right: -0.5px;
  height: 100%;
  background-color: #e0e0e0;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.indicator-pointer {
  position: absolute;
  top: 0;
  width: 16px;
  height: 16px;
  background-color: #e4e3e3;
  border: 2px solid #fff;
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.aqi-legend {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 4px;
}

.legend-text {
  font-size: 12px;
  color: #222;
}

.main-monitor-indicator {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding-top: 10px;
}

.main-monitor-indicator-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.main-monitor-indicator :global {
  .ant-tabs-tab-btn {
    font-size: 12px;
    color: #333;
  }
}

.main-monitor-indicator-content {
  width: 100%;
  height: 100%;
  padding: 0 8px;
}

.main-monitor-indicator-content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-monitor-indicator-content-time {
  font-size: 12px;
  color: #969595;
}

.main-monitor-indicator-content-list {
  padding: 10px 0;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.main-monitor-indicator-content-empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-monitor-indicator-content-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #f2efef;
  background-color: #f8f8f8;
  border-radius: 5px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f8ff;
    box-shadow: 0 2px 8px rgb(24 144 255 / 20%);
    transform: translateY(-2px);
  }
}

.main-monitor-indicator-content-item-name {
  font-size: 14px;
  padding: 5px 0;
  text-align: center;
  width: 100%;
  background-color: #f6f6f6;
  border-bottom: 1px solid #f2efef;
  font-weight: 600;
}

.main-monitor-indicator-content-item-icon {
  width: 50px;
  height: 50px;
  padding: 5px 0;
}

.main-monitor-indicator-content-item-value {
  font-size: 14px;
  line-height: 1.2;
  font-weight: 600;
  color: #5096c0;
  margin: 10px 0;
}

.main-monitor-indicator-content-item-unit {
  font-size: 12px;
  color: #969595;
  padding-bottom: 5px;
}

.right-air-quality {
  width: 100%;
  height: 100%;
}

.right-air-quality-chart {
  width: 100%;
  height: 300px;
  background-color: #fff;
  border-radius: 8px;
  padding: 5px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

  /* 确保图表容器占满可用空间 */
  display: flex;
  flex-direction: column;

  /* 图表容器样式 */
  > div {
    flex: 1;
    min-height: 0;
  }
}

.right-air-quality-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  padding-top: 20px;
  padding-left: 10px;
  margin-bottom: 15px;
}

/* 空气质量表格斑马条纹样式 */
.air-quality-table {
  :global {
    .ant-table-body > table > tbody > tr > td {
      font-size: 12px !important;
    }

    .ant-table-header > table > thead > tr > th {
      font-size: 12px !important;
    }

    .ant-table-tbody > tr:nth-child(even) > td {
      background-color: #fafafa !important;
    }

    .ant-table-tbody > tr:nth-child(odd) > td {
      background-color: #fff !important;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff !important;
    }
  }
}

.right-air-quality-button {
  width: 100px;
  height: 30px;
  background-color: #0574b0;
  color: #fff;
  border: 0;
  margin-left: 20px;
  font-size: 12px;
  font-weight: 400;
}

.modal-footer {
  text-align: right;
  margin-top: 20px;
}

@media screen and (400px <=width <=900px) {
  .statistical-index-content {
    flex-direction: column;
    height: auto;
  }

  .statistical-index-content-left,
  .statistical-index-content-right {
    width: 100%;
    height: auto;
    margin-bottom: 10px;
  }

  .statistical-index-content-left {
    margin-bottom: 10px;
  }
}

:global {
  .ant-picker-input > input {
    font-size: 12px !important;
  }

  .ant-select {
    font-size: 12px !important;
  }
}
