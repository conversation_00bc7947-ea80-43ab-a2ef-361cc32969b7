declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare module 'omit.js';

declare module '*.module.less' {
  const classes: { readonly [key: string]: string };
  export default classes;
}
declare module '*.json' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module 'worker-loader*' {
  class WebpackWorker extends Worker {
    constructor();
  }

  export default WebpackWorker;
}
