.export-popover-content {
	.ant-checkbox-group {
		display: flex;
		flex-direction: column;

		.ant-checkbox-group-item {
			padding: 3px;
		}
	}
}

.even-row {
	&>td {
		background-color: #fff;
	}
}

.odd-row {
	&>td {
		background-color: #f8f9fa;
	}
}

.manual-export-modal {
	.ant-modal-body {
		padding: 8px !important;
		background-color: #E5EAEE;
	}

	.ant-gasleak-form {
		margin-bottom: 8px;
		background-color: #fff;
		padding: 10px;
		height: 120px;

		:global {
			.ant-input,
			.ant-form-item-label>label,
			.ant-select-selector,
			.ant-picker,
			.ant-select-item-option-content,
			.ant-btn {
				font-size: 12px;
			}
		}
	}

	.export-manual-popover {
		padding: 10px;
		display: flex;
		justify-content: end;
		background-color: #fff;

	}

	.export-table {
		background-color: #fff;

		.ant-table-body {
			background-color: #E5EAEE;
		}

		:global {
			.ant-table-thead>tr>th {
				padding: 10px 16px;
				font-size: 12px;
				font-weight: bold;
				height: 40px;
				background-color: #F8F9FA
			}

			.ant-table-tbody>tr>td {
				height: 32px;
				font-size: 12px !important;
				padding: 0 16px !important;
				border-color: rgb(244 244 244) !important;
				line-height: 32px !important;
				border-bottom-width: 1px !important;
				border-bottom-style: solid !important;
				border-right-width: 1px !important;
				border-right-style: solid !important;
			}

			.ant-table-tbody>tr:hover>td {
				background-color: #ECF6FF !important;
			}
		}

	}
}


.drawer-filter-operation {
	width: 100%;
	margin-top: 10px;
	display: flex;
	flex-direction: row-reverse;

	.search-btn {
		margin-left: auto;
		margin-right: 10px;
		background-color: #007EBB;
	}

	.reset-btn {
		margin-right: 20px;
	}

	.info-tip {
		margin-right: auto;
	}
}



.triangle {
	color: #ff0303;
}

.top-button {
	margin: 0 8px;
	padding: 0 25px;
	font-size: 12px;
}