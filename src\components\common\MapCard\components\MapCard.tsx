import { Map, TrackingLayer, PlotDraw, DrawHelper } from 'yth-map';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, InputNumber, Spin, message } from 'antd';
import markImg from '@/assets/position.png';
import style from './MapCard.module.less';
import tdt from '../images/tdt_img.jpg';
// import lantu from '../images/zh_dem.jpg';
// import bing from '../images/zh_img.jpg';

import dicParams from '../../../../utils/dicParams';
import { MapCardExpose, Props, SORN } from './mapTypes';
// 地图初始化
const layers = [
  {
    name: '天地图影像',
    image: tdt,
    show: true,
    list: [
      // 影像
      {
        url: 'http://t0.tianditu.com/img_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'img',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
      // 影像注记
      {
        url: 'http://t0.tianditu.com/cia_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'cia',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
    ],
  },
  {
    name: '天地图(矢量)',
    image: tdt,
    show: false,
    list: [
      // 矢量
      {
        url: 'http://t0.tianditu.com/vec_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'vec',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
      // 矢量注记
      {
        url: 'http://t0.tianditu.com/cva_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'cva',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
    ],
  },
];
// useDebounce 防抖
const useDebounce = <T,>(value: T, delay?: number): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const timeout = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(timeout);
  }, [value, delay]);
  return debouncedValue;
};
class Debounce {
  delay: number;

  timeout: NodeJS.Timeout | null;

  constructor(delay?: number) {
    this.delay = delay || 200;
    this.timeout = null;
  }

  debounceEnd() {
    return new Promise((resolve) => {
      if (this.timeout) {
        clearTimeout(this.timeout);
      }
      this.timeout = setTimeout(() => {
        resolve('success');
      }, this.delay);
    });
  }
}
const propsDefault: Props = {
  mapConfig: {
    width: '100%',
    height: '100%',
    satellite: false,
    zoom: 10,
  },
  position: {},
  operateType: 'add',
  radius: 0,
};

const map = new Map();

// 在函数组件外部先声明displayName
const MapCard = forwardRef((props: Props, ref) => {
  // 点位标记
  const [position, setPosition] = useState<SORN[]>(['', '', '']);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [marker, setMarker] = useState<any>();
  const circleLayerController = useRef<any>(null); // 覆盖半径维护的圆
  const [radiusValue, setRadiusValue] = useState<number>(0); // 覆盖半径的值
  const radiusValueRef = useRef<any>(0);
  const [initLoading, setInitLoading] = useState<boolean>();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const searchDe = useRef(new Debounce());
  // 是否是手机端
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isMobileIphone, setIsMobileIphone] = useState(document.body.clientWidth < 640);

  // 转换位置
  const transferPoint = (value) => {
    const pointObj = {
      x: value[0],
      y: value[1],
      z: value[2] ?? 1,
    };
    return pointObj;
  };

  // 在地图上画圆
  const mapDrawCircle = (v) => {
    if (!circleLayerController.current) return;

    circleLayerController.current.clearAll();
    circleLayerController.current.addCircle({
      x: position[0],
      y: position[1],
      height: 0,
      radius: v,
      showTips: false,
      clampToGround: true,
      fillColor: 'rgba(64,152,252,0.3)',
      fill: true,
      outline: true,
      outlineWidth: 1,
      outlineColor: 'rgba(64,152,252,1)',
      displayCondition: {
        max: 2000,
      },
    });
  };

  const markPositon = (value) => {
    if (!map || !map.layer) return;

    map.layer.clearAll();
    // 绘制
    map.layer.addMarker({
      layerName: 'positionSele',
      point: value,
      img: markImg,
      scale: 1,
      offset: { x: 0, y: -15 },
    });
    if (radiusValueRef.current && radiusValueRef.current !== 0) {
      mapDrawCircle(radiusValueRef.current);
    }
  };

  // 选择位置
  const selectPosition = () => {
    if (!map || !map.layer || !map.plotDraw) return;

    setPosition([]);
    map.layer.clearAll();
    map.plotDraw.activate(0, {
      callback: (geo) => {
        setPosition(geo.geometry.coordinates[0][0]);
        // console.log(geo.geometry.coordinates[0][0]);
        markPositon(transferPoint(geo.geometry.coordinates[0][0]));
      },
    });
  };

  // 重置地图
  const resetMap = (posClear = false) => {
    setInitLoading(false);
    if (props.position && !posClear) return;
    setMarker(null);
    setPosition(['', '', '']);
    if (map) {
      map.resetPlace();
    }
  };

  // 卸载地图
  const destroyMap = () => {
    resetMap();
    if (map) {
      map.resetPlace();
    }
  };

  const getMapCenterAndRadius = () => {
    return {
      x: position[0],
      y: position[1],
      r: radiusValueRef.current,
    };
  };

  const getCamera = () => {
    if (!map) return null;
    return map.getCameraInfo();
  };

  const localSearchResult = (result) => {
    const resultsList: any = document.getElementById('results');
    // console.log(result);
    if (result && result.pois && Array.isArray(result.pois)) {
      const locations = result.pois;
      locations.forEach((location, index) => {
        const listItem = document.createElement('li');
        listItem.textContent = `${index + 1}: ${location.name}`;
        listItem.addEventListener('click', () => {
          // alert(location.lonlat); // Show the inner info
          const nLoc: any = location.lonlat.split(',');
          // console.log(nLoc);
          if (map) {
            map.flyObject({ x: nLoc[0], y: nLoc[1], z: 155 });
          }
        });
        resultsList.appendChild(listItem);
      });
    } else {
      message.error('无数据');
    }
  };

  // 根据关键字搜索
  const searchLocation = async () => {
    const resultsList: any = document.getElementById('results');
    resultsList.innerHTML = '';
    const inputElement: HTMLInputElement | null = document.getElementById(
      'inputSearch',
    ) as HTMLInputElement;
    if (inputElement) {
      const { value } = inputElement;
      const rUrl = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${value}","level":"11","mapBound":"102.546150,24.396308,103.157679,25.132221","queryType":"1","count":"10","start":"0"}&type=query&tk=${dicParams.mapKey}`;
      fetch(rUrl, {
        referrerPolicy: 'strict-origin-when-cross-origin',
        body: null,
        method: 'GET',
        mode: 'cors',
        headers: {},
        credentials: 'omit',
      })
        .then((res) => {
          return res.json();
        })
        .then((data) => {
          localSearchResult(data);
        });
    }
  };

  // 清空搜索结果
  const clearSearch = () => {
    const searchInput: any = document.getElementById('inputSearch')!;
    const resultsList: any = document.getElementById('results');
    searchInput.value = ''; // Clear search input
    resultsList.innerHTML = ''; // Clear search results
  };

  // 初始化地图
  const initMap = async () => {
    try {
      setInitLoading(true);
      let initPoint: any = dicParams.mapCenterStr ?? { x: 0, y: 0, z: 0 };
      // console.log(props.position);
      if (props.position && props.position.x) {
        initPoint = props.position;
        setPosition([props.position.x, props.position.y, props.position.z, props.position.roll]);
        setRadiusValue(props.radius);
        radiusValueRef.current = props.radius;
      } else {
        setPosition([0, 0, 0, 0]);
      }

      map.initMap({
        container: 'map', // 地图承载容器
        sceneModePicker: true, // 二三维选择按钮-显示控制
        sceneModeChose: 3, // 显示模式 三维: 3 二维: 2  默认值: 3
        positionDisplay: true, // 右下角经纬度显示 默认值: true
        compassDisplay: true, // 罗盘显示 默认值: true
        hostAddr: 'http://***************:8096/check',
        // 地图右键点击事件回调，返回参数(position, pt)
        components: true,
        rightClickCall: () => {},
        // 初始位置,地图重置(replace)时用到
        initPlace: {
          point: initPoint,
        },
        layersPro: layers,
        // 地图重置按钮   默认视角, 罗盘中恢复的视角，默认是中国范围
        defaultView: {
          rect: [112.96100967885242, 28.194319720664925, 112.97098015969033, 28.198415260838136],
        },
        // 完成地图加载
        callback: () => {
          circleLayerController.current = new TrackingLayer({ map });
          setInitLoading(false);

          // 地图初始化后再执行这些操作
          if (props.camera && props.camera !== '') {
            // console.log(' -+ camera', props.camera);
            let camerObj: any = {};
            if (props.camera.directionx) {
              camerObj = props.camera;
            } else {
              camerObj = JSON.parse(props.camera);
            }
            map.flyCamera(camerObj, () => {
              if (props.load3DLayer && props.load3DLayer !== '') {
                map.add3DTileLayer({
                  url: props.load3DLayer,
                  center: initPoint,
                });
              }
            });
          } else {
            // console.log(' -- fly object', initPoint);
            map.flyObject(initPoint);
          }

          map.setMapBackground('天地图影像');
          map.plotDraw = new PlotDraw({
            map,
            callback: () => {
              // console.log(geo, "geo");
            },
            editable: false,
          });
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const drawHelper = new DrawHelper({ map });
        },
      });
    } catch {
      setInitLoading(false);
    }
  };

  // 组件初始化阶段的逻辑
  useImperativeHandle<unknown, MapCardExpose>(ref, () => ({
    resetMap,
    destroyMap,
  }));

  useImperativeHandle(props.onRef, () => ({
    resetMap,
    destroyMap,
    getCamera,
    getMapCenterAndRadius,
  }));

  useEffect(() => {
    initMap();
  }, []);

  useEffect(() => {
    if (!position[0] || !position[1]) return;
    markPositon(transferPoint(position));
    if (props.position === position) return;
    props.onCoordChange!(
      JSON.stringify({
        x: position[0],
        y: position[1],
        z: position[2],
        roll: 1,
      }),
    );
  }, [useDebounce(position, 200)]);

  useEffect(() => {
    if (!map) return;
    if (props.position instanceof Array) {
      setPosition(props.position);
    }
  }, [map, props.position]);

  window.onresize = () => {
    setIsMobileIphone(document.body.clientWidth < 640);
  };

  return (
    <Spin spinning={initLoading} size="large" wrapperClassName={style['spin-wrap']}>
      <main className={style['map-container']}>
        <div
          id="map"
          style={{
            width: props.mapConfig?.width || '100%',
            height: props.mapConfig?.height || '100%',
          }}
        />
        <div className={style.toolbar}>
          <div style={{ display: 'flex', alignItems: 'cneter' }}>
            <div className={`${style.info} ${style['ant-card']} ${style['ant-card-bordered']}`}>
              <div style={{ width: '100%', display: 'flex', alignItems: 'center' }}>
                <div
                  style={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '10px',
                    margin: '10px',
                  }}
                >
                  <div style={{ marginRight: '10px' }}>
                    <InputNumber
                      size="small"
                      addonBefore="经度"
                      value={position[0] ? `${position[0]}` : ''}
                      placeholder=""
                      readOnly
                    />
                  </div>
                  <div style={{ marginRight: '10px' }}>
                    <InputNumber
                      size="small"
                      addonBefore="纬度"
                      value={position[0] ? `${position[1]}` : ''}
                      placeholder=""
                      readOnly
                    />
                  </div>
                  <div style={{ marginRight: '10px' }}>
                    <InputNumber
                      size="small"
                      min={0}
                      addonAfter="米"
                      addonBefore="覆盖半径"
                      placeholder="请填写覆盖半径"
                      value={radiusValue}
                      onChange={(v) => {
                        // console.log(v);
                        if (v && v > 0) {
                          setRadiusValue(v);
                          radiusValueRef.current = v;
                          mapDrawCircle(v);
                        }
                      }}
                    />
                  </div>
                  <div style={{ marginRight: '10px' }}>
                    {position.length > 0 && (
                      <Button
                        style={{ marginLeft: '10px' }}
                        size="small"
                        onClick={() => {
                          selectPosition();
                        }}
                        disabled={props.operateType === 'view'}
                        type="primary"
                      >
                        选择位置
                      </Button>
                    )}
                  </div>
                </div>
              </div>
              <div className={style.item}>
                <div className={style['search-bar']}>
                  <div className={style['search-bar-input']}>
                    <input
                      id="inputSearch"
                      className={style.inputArea}
                      placeholder="请输入搜索内容"
                    />
                    <Button
                      type="default"
                      size="small"
                      className={style['map-search-btn']}
                      onClick={searchLocation}
                    >
                      搜索
                    </Button>
                    <Button
                      size="small"
                      className={style['map-clear-btn']}
                      type="default"
                      onClick={clearSearch}
                    >
                      重置
                    </Button>
                  </div>
                  <div className={style['search-bar-result']}>
                    <div id="results" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </Spin>
  );
});

// 添加displayName
MapCard.displayName = 'MapCard';

// 设置默认值
MapCard.defaultProps = propsDefault;

export default MapCard;
