/* TraceObjectSelector 组件样式 */
.trace-object-selector {
  padding: 20px;

  /* 选中行样式 */
  :global(.selected-row) {
    background-color: #e6f7ff !important;

    &:hover {
      background-color: #bae7ff !important;
    }
  }

  /* 表格行悬停样式 */
  :global(.ant-table-tbody > tr:hover) {
    background-color: #f5f5f5;
  }

  /* 表格行过渡动画 */
  :global(.ant-table-tbody > tr) {
    transition: background-color 0.2s;
  }

  /* Transfer 组件样式 */
  .transfer-container {
    width: 100%;
  }

  /* 表格容器样式 */
  .table-container {
    height: 400px;
    overflow: hidden;
  }

  /* 表格样式 */
  .table {
    height: 100%;
  }

  /* 可点击行样式 */
  .clickable-row {
    cursor: pointer;
    user-select: none;
  }
}
