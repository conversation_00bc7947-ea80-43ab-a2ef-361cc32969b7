import { CurrentUser } from '@/Constant';
import expression from './expression';

interface TreeNode {
  unitName?: string;
  name?: string;
  children?: TreeNode[];
  code?: string;
  fatherId?: string;
  text?: string;
  disabled?: boolean;
  [key: string]: unknown;
}

interface UserNode {
  realName?: string;
  name?: string;
  type?: string;
  [key: string]: unknown;
}

type FilterValue = string | number | boolean | null | undefined;

// 给获取的组织机构树添加name
export const formatTree: (list: TreeNode[]) => TreeNode[] = (list: TreeNode[]): TreeNode[] => {
  if (!list || list.length === 0) return [];
  const arr: TreeNode[] = [];
  list.forEach((ele) => {
    const obj: TreeNode = { ...ele };
    obj.name = obj.unitName;
    if (obj.children && obj.children.length > 0) {
      obj.children = formatTree(obj.children);
    }
    arr.push(obj);
  });
  return arr;
};

// 给获取的人员添加name和type
export const formatUser: (list: UserNode[]) => UserNode[] = (list: UserNode[]): UserNode[] => {
  if (!list || list.length === 0) return [];
  const arr: UserNode[] = [];
  list.forEach((ele) => {
    const obj: UserNode = { ...ele };
    obj.name = obj.realName;
    obj.type = 'user';
    arr.push(obj);
  });
  return arr;
};

// 筛选条件格式
export const formatFilterCode: (obj: Record<string, unknown>) => Record<string, FilterValue> = (
  obj: Record<string, unknown>,
): Record<string, FilterValue> => {
  const keys: string[] = Object.keys(obj);
  const data: Record<string, FilterValue> = {};
  if (keys.length === 0) return {};

  keys.forEach((item) => {
    if (obj[item] instanceof Array) {
      // 是否是数组
      const arr: Array<{ code?: number | string; id?: string | number }> = obj[item] as Array<{
        code?: number | string;
        id?: string | number;
      }>;
      if (arr.length === 1) {
        if (arr[0].code !== undefined) {
          data[item] = arr[0].code;
        } else if (arr[0].id !== undefined) {
          data[item] = arr[0].id;
        } else {
          data[item] = arr[0] as string;
        }
      } else if (arr.length > 1) {
        data[item] = arr.map((li) => li.code).join(',');
      } else {
        data[item] = '';
      }
    } else if (obj[item] && typeof obj[item] === 'object') {
      // 是否是对象
      const objItem: { id?: string | number; code?: string | number } = obj[item] as {
        id?: string | number;
        code?: string | number;
      };
      if (objItem.id !== undefined) {
        data[item] = objItem.id;
      } else if (objItem.code !== undefined) {
        data[item] = objItem.code;
      } else {
        data[item] = '';
      }
    } else if (obj[item]) {
      // 是否有值
      data[item] = obj[item] as string;
    } else {
      data[item] = '';
    }
  });
  return data;
};

// 指定长度和基数生成uuid
export const getUuid: (len?: number, radix?: number) => string = (
  len?: number,
  radix?: number,
): string => {
  const chars: string[] = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split(
    '',
  );
  const uuid: string[] = [];
  const actualRadix: number = radix || chars.length;

  if (len) {
    for (let i: number = 0; i < len; i += 1) {
      uuid[i] = chars[Math.floor(Math.random() * actualRadix)];
    }
  } else {
    let r: number;

    // rfc4122 requires these characters
    uuid[8] = '-';
    uuid[13] = '-';
    uuid[18] = '-';
    uuid[23] = '-';
    uuid[14] = '4';

    for (let i: number = 0; i < 36; i += 1) {
      if (!uuid[i]) {
        r = Math.floor(Math.random() * 16);
        // 使用数学运算代替位运算
        uuid[i] = chars[i === 19 ? Math.floor(r % 4) + 8 : r];
      }
    }
  }
  return uuid.join('');
};

// 手机号校验
export const validatePhone: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.mobilePhone.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('手机号格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 数字校验
export const validateNumber: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.positive.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('请输入数字！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 获取父节点宽高
export const getParentSize: (node: HTMLElement | null) => { width?: number; height?: number } = (
  node: HTMLElement | null,
): { width?: number; height?: number } => {
  const parent: HTMLElement | null = node?.parentNode as HTMLElement;
  const width: number | undefined = parent?.clientWidth;
  const height: number | undefined = parent?.clientHeight;
  return { width, height };
};

// 统一社会信用代码 校验
export const validateCredit: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.credit.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('统一社会信用代码 格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 邮箱 校验
export const validateEmail: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.email.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('邮箱 格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

interface CodeItem {
  code: string;
  text?: string;
  [key: string]: unknown;
}

// 多选组件code转换
export const muiltCodeGet: (data: CodeItem[]) => string = (data: CodeItem[]): string => {
  let str: string = '';
  data.forEach((it) => {
    str = `${str}${it.code},`;
  });
  if (str.length > 0) {
    str = str.substring(0, str.length - 1);
  }
  return str;
};

// 多选组件文字转换
export const muiltTextGet: (data: { text: string }[]) => string = (
  data: { text: string }[],
): string => {
  let str: string = '';
  data.forEach((it) => {
    str = `${str}${it.text},`;
  });
  if (str.length > 0) {
    str = str.substring(0, str.length - 1);
  }
  return str;
};

interface ValueItem {
  text: string;
  code: string;
}

// 多选组件条件转换
export const muiltVlueGet: (
  data: Record<string, string | undefined>,
  text: string,
  code: string,
) => ValueItem[] = (
  data: Record<string, string | undefined>,
  text: string,
  code: string,
): ValueItem[] => {
  const value: ValueItem[] = [];

  data[text]?.split(',').forEach((item, index) => {
    data[code]?.split(',').forEach((it, idx) => {
      if (index === idx && item !== '') {
        value.push({
          text: item,
          code: it,
        });
      }
    });
  });

  return value;
};

// 解析url 获取businessId
export const parseUrlParams: (url?: string) => Record<string, string | boolean> = (
  url?: string,
): Record<string, string | boolean> => {
  const params: Record<string, string | boolean> = {};
  if (!url || url === '' || typeof url !== 'string') {
    return params;
  }

  const paramsStr: string | undefined = url.split('?')[1];
  if (!paramsStr) {
    return params;
  }

  const paramsArr: string[] = paramsStr.replace(/&|=/g, ' ').split(' ');

  for (let i: number = 0; i < paramsArr.length / 2; i += 1) {
    const value: string = paramsArr[i * 2 + 1];

    if (value === 'true') {
      params[paramsArr[i * 2]] = true;
    } else if (value === 'false') {
      params[paramsArr[i * 2]] = false;
    } else {
      params[paramsArr[i * 2]] = value;
    }
  }

  return params;
};

// 列表转树
export const listToTree: (data: TreeNode[]) => TreeNode[] = (data: TreeNode[]): TreeNode[] => {
  const result: TreeNode[] = [];

  if (!Array.isArray(data)) {
    return result;
  }

  // 创建数据副本以避免修改原始数据
  const dataCopy: TreeNode[] = data.map((item) => ({ ...item }));

  // 删除所有children属性，以便重新构建树
  const cleanedData: TreeNode[] = dataCopy.map((item) => {
    const newItem: TreeNode = { ...item };
    delete newItem.children;
    return newItem;
  });

  const map: Record<string, TreeNode> = {};

  // 填充map，使用code作为键
  cleanedData.forEach((item) => {
    const itemWithText: TreeNode = { ...item, text: item.name };
    map[itemWithText.code as string] = itemWithText;
  });

  // 构建树
  cleanedData.forEach((item) => {
    const parent: TreeNode | undefined = map[item.fatherId as string];

    if (parent) {
      if (!parent.children) {
        // 创建父节点的副本，避免直接修改
        map[parent.code as string] = {
          ...parent,
          children: [...(parent.children || []), item],
        };
      } else {
        map[parent.code as string] = {
          ...parent,
          children: [...parent.children, item],
        };
      }
    } else {
      result.push(item);
    }
  });

  return result;
};

// 多选组件文字对象转换
export const muiltTextListGet: (data: { text: string }[]) => string = (
  data: { text: string }[],
): string => {
  let str: string = '';
  data.forEach((it) => {
    str = `${str}${it.text},`;
  });
  if (str.length > 0) {
    str = str.substring(0, str.length - 1);
  }
  return str;
};

interface DeptItem {
  name: string;
  code: string;
  id: string;
  type: string;
}

// 多选组件数组转换
export const muiltVlueArryGet: (text?: string, ids?: string, code?: string) => DeptItem[] = (
  text?: string,
  ids?: string,
  code?: string,
): DeptItem[] => {
  const value: DeptItem[] = [];

  if (text && ids && code) {
    const textArr: string[] = text.split(',');
    const idsArr: string[] = ids.split(',');
    const codeArr: string[] = code.split(',');

    textArr.forEach((item, index) => {
      if (index < codeArr.length && index < idsArr.length && item !== '') {
        value.push({
          name: item,
          code: codeArr[index],
          id: idsArr[index],
          type: 'dept',
        });
      }
    });
  }

  return value;
};

// 递归禁用父级数据
export const disableHasChirld: (list: TreeNode[]) => TreeNode[] = (
  list: TreeNode[],
): TreeNode[] => {
  return list.map((item) => {
    if (item.children && item.children.length > 0) {
      return {
        ...item,
        disabled: true,
        children: disableHasChirld(item.children),
      };
    }
    return item;
  });
};

// 多选组件文字对象转换
export const muiltNameListGet: (data: { name: string }[]) => string = (
  data: { name: string }[],
): string => {
  let str: string = '';
  data.forEach((it) => {
    str = `${str}${it.name},`;
  });
  if (str.length > 0) {
    str = str.substring(0, str.length - 1);
  }
  return str;
};

// 车牌号校验
export const validatePlate: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.carPlate.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('车牌号格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 检查button是否可以显示
export const AUTHBtn: (userId?: string) => boolean = (userId?: string): boolean => {
  if (!CurrentUser().id) {
    return false; // 没有id就false
  }

  if (userId && userId !== '') {
    if (CurrentUser().id === 'admin') {
      return true; // 如果是超管就不锁权限
    }

    // 数据创建人id与当前用户id相同则有权限
    return userId === CurrentUser().id;
  }

  return true;
};
