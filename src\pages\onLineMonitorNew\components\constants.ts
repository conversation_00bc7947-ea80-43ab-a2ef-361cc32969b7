import moment from 'moment';
import { DateListTypes, MonitorTypeConfig, OnlineMonitorTagType } from './types';

/**
 * 时间类型列表
 * 包含不同时间类型的配置信息
 * 根据remark字段匹配对应的数据类型配置
 */
export const dateList: DateListTypes[] = [
  {
    text: '实时数据', // 时间类型文本
    dataCode: '2011', // 时间类型代码，对应testData中的remark字段值
    key: '1',
    format: 'YYYY-MM-DD HH:mm:ss', // 日期格式：精确到秒
    over: 7, // 时间范围限制：最大7天
    pickerType: 'dateTime', // 日期时间选择器类型：可选时分秒
    defaultRange: () => [moment().startOf('day'), moment()], // 默认时间范围：当天开始到现在
  },
  {
    text: '时均值', // 时间类型文本
    dataCode: '2061', // 时间类型代码，对应testData中的remark字段值
    key: '2',
    format: 'YYYY-MM-DD HH', // 日期格式：精确到小时
    over: 7, // 时间范围限制：最大7天
    pickerType: 'dateTime', // 日期时间选择器类型：可选到时
    defaultRange: () => [moment().subtract(1, 'days').startOf('day'), moment()], // 默认时间范围：1天前到现在
  },
  {
    text: '日均值', // 时间类型文本
    dataCode: '2031', // 时间类型代码，对应testData中的remark字段值
    key: '3',
    format: 'YYYY-MM-DD', // 日期格式：精确到日
    over: 30, // 时间范围限制：最大30天
    pickerType: 'date', // 日期选择器类型：只选日期
    defaultRange: () => [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')], // 默认时间范围：7天
  },
  {
    text: '年均值', // 时间类型文本
    dataCode: '9999', // 时间类型代码，对应testData中的remark字段值
    key: '4',
    format: 'YYYY', // 日期格式：只显示年份
    pickerType: 'year', // 年份选择器类型：只能选一个年份
    defaultRange: () => moment(), // 默认时间范围：当前年份
  },
];

/**
 * 均值和累计值数据标识
 * 用于表格列显示和数据处理
 */
export const dataformat = [
  { value: '均值', key: 'avg' }, // 均值数据标识
  { value: '累计值', key: 'sum' }, // 累计值数据标识
];

/**
 * 监测类型配置映射
 * 不同监测类型的配置信息
 */
export const monitorTypeConfigs: Record<string, MonitorTypeConfig> = {
  // 环境质量监测
  envQuality: {
    onLineMonitorType: 'Air', // 监测类型值，传递给后端接口
    exportName: '环境质量监测', // 导出报表名称
    monitorKey: 'A22A08A06', // 字典键值，用于获取监测类型列表
  },

  // 污染源监测
  wasteGas: {
    onLineMonitorType: 'wasteAir', // 监测类型值，传递给后端接口
    exportName: '污染源监测', // 导出报表名称
    filterMonitorList: true, // 是否过滤监测类型列表
    filterCondition: (item) => item.code !== 'A22A08A07A05', // 过滤条件：排除公用工程监测类型
    monitorKey: 'A22A08A07', // 字典键值，用于获取监测类型列表
  },
};

/**
 * 在线监测标签列表
 * 不同标签的配置信息，颜色代表不同含义：
 * - 白色：正常状态（正常）
 * - 绿色：维护状态（维护）
 * - 红色：严重异常（停运、故障）
 * - 橙色：警告状态（校准）
 * - 蓝色：人工操作（手工输入）
 * - 紫色：数据异常（超上限）
 * - 灰色：通信问题（通信异常）
 */
export const tagList: OnlineMonitorTagType[] = [
  {
    text: '正常',
    key: 'N',
    color: '#FFFFFF', // 保持原来的白色
    borderColor: '#000000',
  },
  {
    text: '停运',
    key: 'F',
    color: '#F32430', // 保持原来的红色 - 表示设备停止运行
  },
  {
    text: '维护',
    key: 'M',
    color: '#40AB3E', // 保持原来的绿色 - 表示设备维护中
  },
  {
    text: '手工输入',
    key: 'S',
    color: '#1890FF', // 蓝色 - 表示人工录入数据
  },
  {
    text: '故障',
    key: 'D',
    color: '#FF4D4F', // 红色 - 表示设备故障
  },
  {
    text: '校准',
    key: 'C',
    color: '#FA8C16', // 橙色 - 表示设备校准中
  },
  {
    text: '超上限',
    key: 'T',
    color: '#722ED1', // 紫色 - 表示数据超出上限
  },
  {
    text: '通信异常',
    key: 'B',
    color: '#8C8C8C', // 灰色 - 表示通信故障
  },
];
