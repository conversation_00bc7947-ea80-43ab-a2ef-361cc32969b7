import { rbacRequest, baseRequest, formRequest } from '../request';
import type { ApiResponse } from './analysisApi';

/*
  请求组织机构树
*/
export const queryUnitTreeData: () => Promise<Record<string, string>[]> = async () => {
  const url: string = '/unit/unitTree';
  const resp: ApiResponse = await rbacRequest.get(url);
  const { data = [] } = resp ?? {};

  return data as Record<string, string>[];
};

/*
  快开查询当前用户所属角色
*/
export const queryCurrentUserRoles: (uid: string) => Promise<Record<string, string>> = async (
  uid,
) => {
  const url: string = `/role/roleDetail?userId=${uid}`;
  const resp: ApiResponse<{ list: Record<string, string>[] }> = await rbacRequest.get(url);
  const { data = {} } = resp ?? {};

  return data;
};

/*
  通过当前登陆用户查询组织机构列表
*/
export const queryOrganizationTreeListByLoginUser: () => Promise<
  Record<string, string>[]
> = async () => {
  const url: string = '/baseCommon/queryOrganizationTreeListByLoginUser';
  const resp: ApiResponse<Record<string, string>[]> = await baseRequest.get(url);
  const { data = [] } = resp ?? {};

  return data as Record<string, string>[];
};

// ============================企业信息=====================================
/*
  基础信息 - 企业信息 - 查询企业外列表
*/
export const queryAllCompanyList: (
  params: Record<string, string | number>,
) => Promise<ApiResponse> = async (params = {}) => {
  const url: string = '/baseEnterpriseInfo/queryByPage';
  const resp: ApiResponse = await baseRequest.post(url, { data: params });
  return resp;
};

/** 查询当前用户信息 */
export const getCurrentUser: () => Promise<{
  company: Record<string, string>;
  user: Record<string, string | number>;
}> = async () => {
  const resp: ApiResponse = await rbacRequest.get('/user/appCurrentUser');

  const { data = [] } = resp ?? {};

  return data as {
    company: Record<string, string>;
    user: Record<string, string | number>;
  };
};

// 获取字典子节点数据
export const getDictionary: (
  params: Record<string, string | number>,
) => Promise<ApiResponse['data']> = async (params = {}) => {
  const resp: ApiResponse = await formRequest.post('/dataDictionary/query', {
    data: params,
  });
  return resp?.data || [];
};

// 查询登陆用户默认园区 | 类型数据
export type ByParkCodeType = {
  parkName?: string;
  parkCode?: string;
  center?: string;
  lensTowards?: string;
};

// 查询登陆用户默认园区 | 接口方法
export const queryByParkCode: () => Promise<ByParkCodeType> = async () => {
  const resp: ApiResponse<{
    informationVo: { parkName: string; parkCode: string };
    center: string;
    lensTowards: string;
  }> = await baseRequest.get(`/baseCampusExtend/queryByParkCode`);
  let result: ByParkCodeType = {};
  if (resp.code === 200) {
    result = {
      parkName: resp.data?.informationVo?.parkName ?? '',
      parkCode: resp.data?.informationVo?.parkCode ?? '',
      center: resp.data?.center ?? '',
      lensTowards: resp.data?.lensTowards ?? '',
    };
  }
  return result;
};
