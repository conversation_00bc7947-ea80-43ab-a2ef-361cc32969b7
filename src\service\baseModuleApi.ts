import { rbacRequest, baseRequest, smpRequest, formRequest } from '../request';

/*
  请求组织机构树
*/
export const queryUnitTreeData = async () => {
  const url = '/unit/unitTree';
  const resp = await rbacRequest.get(url);
  const { data = [] } = resp ?? {};

  return data;
};
/*
  请求组织机构列表
*/
export const queryUnitListData = async () => {
  const url = '/unit/tree';
  const resp = await rbacRequest.get(url);
  const { data = [] } = resp ?? {};

  return data;
};
/*
  快开用户管理
*/
export const queryKKUserListData = async (params = {}) => {
  const url = '/user/page?current=1';
  const resp = await rbacRequest.get(url, { data: params });
  const { data = [] } = resp ?? {};

  return data;
};

/*
  快开查询当前用户信息
*/
export const queryKKCurrentUser = async () => {
  const url = '/user/currentUser';
  const resp = await rbacRequest.get(url);
  const { data = [] } = resp ?? {};

  return data;
};

/*
  快开查询当前用户所属角色
*/
export const queryCurrentUserRoles = async (uid) => {
  const url = `/role/roleDetail?userId=${uid}`;
  const resp = await rbacRequest.get(url);
  const { data = [] } = resp ?? {};

  return data;
};

/*
  通过当前登陆用户查询组织机构列表
*/
export const queryOrganizationTreeListByLoginUser = async () => {
  const url = '/baseCommon/queryOrganizationTreeListByLoginUser';
  const resp = await baseRequest.get(url);
  const { data = [] } = resp ?? {};

  return data;
};

// ============================企业信息=====================================
/*
  基础信息 - 企业信息 - 查询企业外列表
*/
export const queryAllCompanyList = async (params = {}) => {
  const url = '/baseEnterpriseInfo/queryByPage';
  const resp = await baseRequest.post(url, { data: params });
  return resp;
};

/** 查询当前用户信息 */
export const getCurrentUser = async () => {
  const resp = await rbacRequest.get('/user/appCurrentUser');

  const { data = [] } = resp ?? {};

  return data;
};

// 获取字典子节点数据
export const getDictionary = async (params = {}) => {
  const resp = await formRequest.post('/dataDictionary/query', {
    data: params,
  });
  const { data = {} } = resp ?? {};
  return data;
};

// 查询园区内已绑定的企业不带权限 | 类型数据
type BindEnterprisesByCurrentUserType = {
  unitName?: string;
  id?: string;
  unitType?: string;
  text?: string;
  code?: string;
  unitCode?: string;
};

// 查询园区内已绑定的企业不带权限	| 接口方法
export const queryBindEnterprisesByCurrentUser = async (params = {}) => {
  const resp = await baseRequest.get(`/baseCommon/queryBindEnterprisesByCurrentUserCampus`, {
    data: params,
  });
  let result: BindEnterprisesByCurrentUserType[] = [];
  if (resp.code === 200) {
    result = resp.data.map((item) => {
      return {
        unitName: item.orgIdText,
        id: item.orgId,
        unitType: '0',
        text: item.orgIdText,
        code: item.orgId,
        unitCode: item.companyCode,
      };
    });
  }
  return result;
};

// 查询第三方平台地址接口
export const queryThirdPartyMonitoringPlatform = async (orgId, queryCampus?) => {
  const resp = await smpRequest.post(
    `/thirdPartyMonitoringPlatform/queryByOrgId?orgId=${orgId}&queryCampus=${queryCampus}`,
  );

  return resp;
};

// 查询登陆用户默认园区 | 类型数据
type ByParkCodeType = {
  parkName?: string;
  parkCode?: string;
  center?: string;
  lensTowards?: string;
};

// 查询登陆用户默认园区 | 接口方法
export const queryByParkCode = async () => {
  const resp = await baseRequest.get(`/baseCampusExtend/queryByParkCode`);
  let result: ByParkCodeType = {};
  if (resp.code === 200) {
    result = {
      parkName: resp.data?.informationVo?.parkName ?? '',
      parkCode: resp.data?.informationVo?.parkCode ?? '',
      center: resp.data?.center ?? '',
      lensTowards: resp.data?.lensTowards ?? '',
    };
  }
  return result;
};
