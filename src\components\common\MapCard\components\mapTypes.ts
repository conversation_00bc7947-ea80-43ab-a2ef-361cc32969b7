export type SORN = string | number;

export interface Address {
  addressComponent: {
    citycode: string;
    adcode: string;
    businessAreas: string[];
    neighborhoodType: string;
    neighborhood: string;
    province: string;
    street: string;
    streetNumber: string;
    township: string;
  };
  crosses: string[];
  formattedAddress: string;
  pois: string[];
  roads: string[];
}
export interface CoordChangeProps {
  lng: SORN;
  lat: SORN;
  position?: SORN[];
  address?: Address;
  formattedAddress?: string;
}

export interface Results {
  address: string;
  distance: number;
  id: string;
  location: any;
  name: string;
}

export interface Props {
  mapConfig?: {
    // 地图配置
    width?: string; // 地图宽度
    height?: string; // 地图高度
    center?: number[]; // 地图中心
    zoom?: number; // 地图层级
    satellite?: boolean; // 是否开启卫星图
  };
  load3DLayer?: string;
  onRef?: any;
  camera?: any;
  areaList?: any;
  position?: any; // [lng,lat]
  onCoordChange?: (props: any) => void;
  operateType: string;
  confirmButton?: (props) => void;
  cancelButton?: () => void;
  radius?: any;
}

export interface MapCardExpose {
  resetMap: (posClear?: boolean) => void;
  destroyMap: () => void;
}
