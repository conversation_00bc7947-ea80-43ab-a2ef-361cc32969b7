import React, { useEffect, useState } from 'react';
import type { IYTHColumnProps } from 'yth-ui/es/components/list/index';
import {
  Modal,
  message,
  Button,
  Popover,
  Empty,
  Checkbox,
  Table,
  Form,
  Select,
  DatePicker,
  Row,
  Col,
} from 'antd';
import moment from 'moment';
import formApi from '@/service/formApi';
import {
  queryUnitInfoByType,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  exportManualFillData,
  queryMonitorIndexReportData,
} from '@/service/envApi';
import style from './fill.module.less';

type objType = Record<string, string | number>;

type PropsTypes = {
  open: boolean;
  /** 回调函数 */
  onResult?: (result?: objType) => void;
  /** 根据不同类型 导出时文件名不同 */
  exportType: 'envQuality' | 'pollutionSource';
  /** 传入的 环境质量和污染源 字典值 */
  monitorData: 'A22A08A06' | 'A22A08A07';
};

/** 报表类型 types */
type ReportTypes = {
  name: '日报表' | '月报表';
  code: 'date' | 'month';
  key: string;
};

const lastCol = {
  cou: '个数',
  min: '最小值',
  max: '最大值',
  mean: '均值',
  total: '合计',
};

type collistType = {
  code: string;
  id: string;
  [key: string]: string;
};

const dataformat = [
  { value: '均值', key: 'avg' },
  { value: '累计值', key: 'sum' },
];

// 报表类型
const reportTypeData: ReportTypes[] = [
  { name: '日报表', code: 'date', key: '1' },
  { name: '月报表', code: 'month', key: '2' },
];

/**
 * @description 导出modal
 * @returns
 */
const exportReportModl: React.FC<PropsTypes> = ({ open, onResult, exportType, monitorData }) => {
  const [form] = Form.useForm();
  const [checkValue, setCheckValue] = useState<React.Key[]>([]); // 因子配置选择的可见表头项
  const [columnList, setColumnList] = useState<objType[]>([]); // 原始column表头数据
  const [filterCol, setFilterCol] = useState<IYTHColumnProps[]>([]); // 根据因子配置筛选的column数据
  const [resData, setResData] = useState<objType[]>([]); // 列表返回数据
  const [loading, setLoading] = useState<boolean>(false);
  const [listloading, setListLoading] = useState<boolean>(false); // table加载状态
  const [reportType, setReportType] = useState<ReportTypes['code']>('date'); // 报表类型
  const [showEnd, setShowEnd] = useState<boolean>(false); // 根据条件判断是否展示结束时间
  const [deviceDataList, setDeviceDataList] = useState<collistType[]>([]);
  const [monitorList, setMonitorList] = useState<objType[]>([]); // 监测类型
  const [companyList, setCompanyList] = useState<objType[]>();
  const [frequencyList, setFrequencyList] = useState<objType[]>([]); // 采集频率
  const [dataSourceTypeKey, setDataSourceTypeKey] = useState<string>(''); // 数据源
  const [selectedEquipId, setSelectedEquipId] = useState<string>(''); // 当前选中的设备ID

  /**
   * 获取默认时间范围
   * @param dataSourceType 数据源类型
   * @returns 默认的开始和结束时间
   */
  const getDefaultTimeRange = (dataSourceType: string) => {
    const now = moment();

    switch (dataSourceType) {
      case '2011': // 实时数据：当天开始到当前时间
        return {
          startDate: now.clone().startOf('day'),
          endDate: now.clone(),
        };
      case '2061': // 时均数据：当天开始到当前时间
        return {
          startDate: now.clone().startOf('day'),
          endDate: now.clone(),
        };
      case '2031': // 日均数据：当天往前推6天到当天
        return {
          startDate: now.clone().subtract(6, 'days').startOf('day'),
          endDate: now.clone().endOf('day'),
        };
      case '9999': // 年均数据：当前年份
        return {
          startDate: now.clone().startOf('year'),
          endDate: undefined, // 年均数据不需要结束时间
        };
      default:
        return {
          startDate: now.clone().startOf('day'),
          endDate: now.clone(),
        };
    }
  };

  // 获取采集频率
  const getFrequencyList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: 'A23A04',
      },
      currentPage: 0,
      pageSize: 0,
    });
    setFrequencyList([...list]);
    const defaultFrequency = list[0]?.remark || '';

    // 获取默认时间范围
    const defaultTimeRange = getDefaultTimeRange(defaultFrequency);

    form.setFieldsValue({
      dataSourceType: defaultFrequency,
      startDate: defaultTimeRange.startDate,
      endDate: defaultTimeRange.endDate,
    });
    setDataSourceTypeKey(defaultFrequency);

    // 根据默认采集频率设置是否显示结束时间选择器
    if (defaultFrequency === '9999') {
      setShowEnd(false);
    } else {
      setShowEnd(true);
    }
  };

  /**
   * @description 获取监测类型
   */
  const getMonitorList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: monitorData,
      },
      currentPage: 0,
      pageSize: 0,
    });
    if (monitorData === 'A22A08A07') {
      const newList = list?.filter((item) => item.code !== 'A22A08A07A05') || [];
      setMonitorList([...newList]);
    } else {
      setMonitorList([...list]);
    }
  };

  /**
   * @description 固定表头
   */
  const fixedCol: IYTHColumnProps[] = [
    {
      code: 'collectTime',
      name: '监测时间',
      width: 180,
      title: '监测时间',
      align: 'center',
      dataIndex: 'collectTime',
      sorter: true,
      render: (_r, record) => {
        if (record.stat) {
          return lastCol[record.stat];
        }
        return record.monitorTime || '-';
      },
      fixed: 'left',
      key: 'collectTime',
    },
  ];

  /**
   * @description 格式化数据
   * @param treeData
   * @returns
   */
  type dealTreeDataType = (
    list?: Array<{
      code?: string;
      name?: string;
      measureUnit?: string;
      firstLevelMax?: string;
      firstLevelMin?: string;
      isTotalizeValue?: string;
    }>,
  ) => IYTHColumnProps[];
  const dealTreeData: dealTreeDataType = (treeData) => {
    const data: IYTHColumnProps[] = treeData?.map((item, index) => {
      const newItem: IYTHColumnProps = {
        ...item,
        key: (item.code as string) + index,
        dataIndex: (item.code as string) + index,
        title: item.name,
        width: 180,
        align: 'center',
        children: [],
      };
      newItem.children = dataformat.map((str) => {
        return {
          key: item.code + str.key + index,
          dataIndex: item.code + str.key + index,
          align: 'center',
          title: str.value,
          children: [
            {
              key: item.code + str.key,
              dataIndex: item.code + str.key,
              align: 'center',
              title: item.measureUnit,
              render: (_r, record) => {
                const newVal = item.code + str.key;
                if (record[newVal]) {
                  if (record.stat) {
                    return record[newVal] ? Math.trunc(Number(record[newVal]) * 1000) / 1000 : '-';
                  }
                  if (
                    item.firstLevelMax &&
                    item.firstLevelMin &&
                    (record[newVal] > item.firstLevelMax || record[newVal] < item.firstLevelMin)
                  ) {
                    return (
                      <div className={style.triangle}>
                        {record[newVal] ? Math.trunc(Number(record[newVal]) * 1000) / 1000 : '-'}
                      </div>
                    );
                  }
                  return record[newVal] ? Math.trunc(Number(record[newVal]) * 1000) / 1000 : '-';
                }
                if (record[newVal] === 0) {
                  return '0';
                }
                return '-';
              },
            },
          ],
        };
      });
      if (!item.isTotalizeValue || item.isTotalizeValue === '0') {
        delete newItem.children[1];
      }

      return newItem;
    });
    return data;
  };

  /**
   * @description 简单格式化时间
   * @param dataType // 报表类型
   * @param dataSource  // 数据源
   * @param startTime
   * @param endTime
   * @returns
   */
  const changeTimeFormat = (
    dataType: ReportTypes['code'],
    dataSource: string,
    startTime,
    endTime,
  ) => {
    let newStart = '';
    let newEnd = '';
    if (dataType === 'month') {
      newStart = moment(startTime).startOf('month').format('YYYY-MM-DD HH:mm:ss');
      newEnd = moment(startTime).endOf('month').format('YYYY-MM-DD HH:mm:ss');
    } else {
      switch (dataSource) {
        // 查找数据源对应的配置，处理年份类型
        case '9999':
          newStart = moment(startTime).startOf('year').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(startTime).endOf('year').format('YYYY-MM-DD HH:mm:ss');
          break;
        // 日数据
        case '2031':
          newStart = moment(startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          break;
        // 实时数据和时均数据
        case '2011': // 实时数据 - 保持用户选择的具体时间（到秒）
          newStart = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        case '2061': // 时均数据 - 保持用户选择的具体时间（到小时）
          newStart = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        default:
          newStart = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
      }
    }
    return { newStart, newEnd };
  };

  /**
   * 检查时间范围是否超过限制
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param dataSourceType 数据源类型
   * @returns 是否超过限制
   */
  const checkDateRangeLimit = (
    startTime: moment.Moment,
    endTime: moment.Moment,
    dataSourceType: string,
  ): boolean => {
    if (!startTime || !endTime) return false;

    const diffDays = endTime.diff(startTime, 'days');

    // 实时数据和时均数据最大只能选择7天
    if (dataSourceType === '2011' || dataSourceType === '2061') {
      if (diffDays > 7) {
        message.warning('实时数据和时均数据最大只能选择7天范围');
        return true;
      }
    }

    // 日均数据最大只能选择30天
    if (dataSourceType === '2031') {
      if (diffDays > 30) {
        message.warning('日均数据最大只能选择30天范围');
        return true;
      }
    }

    return false;
  };

  /**
   * @description 获取列表数据
   * @param list
   */
  const getDataList: (id?: string) => void = async (id) => {
    const { startDate, endDate, deviceCode, formCompany, monitorType, dataSourceType } =
      form.getFieldsValue();
    const aaa = {
      dataType: reportType,
      dataSource: dataSourceType ?? '',
      startTime: startDate,
      endTime: endDate,
    };
    const equipId = id ?? deviceDataList.find((str) => str.code === deviceCode)?.id;

    // 检查时间范围限制
    if (checkDateRangeLimit(aaa.startTime, aaa.endTime, aaa.dataSource)) {
      return; // 超过限制，不执行查询
    }

    const { newStart, newEnd } = changeTimeFormat(
      aaa.dataType,
      aaa.dataSource,
      aaa.startTime,
      aaa.endTime,
    );
    setListLoading(true);
    const responseData = await queryMonitorIndexReportData({
      monitorType,
      equipCd: deviceCode || '',
      equipId: equipId ?? '',
      orgCd: formCompany,
      startTm: newStart,
      endTm: newEnd,
      dateType: reportType === 'month' ? '4' : dataSourceType,
    });
    if (responseData?.code && responseData?.code === 200 && responseData?.data) {
      setResData(Array.isArray(responseData.data) ? [...responseData.data] : []);
    } else {
      setResData([]);
    }
    setListLoading(false);
  };

  /**
   * @description 获取表头列数据并更改结构
   * 使用当前选中的设备ID和监测频率获取监测指标
   */
  const getColData = async () => {
    // 检查必要参数：设备ID和监测频率都必须存在
    if (!selectedEquipId || !dataSourceTypeKey) {
      // 缺少必要参数时，只显示时间列
      setFilterCol([...fixedCol]);
      return;
    }

    // 清空现有数据
    setColumnList([]);
    setCheckValue([]);
    // 调用API获取监测指标
    const data = await queryMonitorIndex(selectedEquipId, dataSourceTypeKey);
    if (data.code === 200 && data.data instanceof Array && data.data.length > 0) {
      const newList = data?.data.map((item) => item.code) as string[];
      const newData = data.data as objType[];
      setColumnList([...newData]);
      setFilterCol([...fixedCol, ...dealTreeData(data.data)]);
      setCheckValue([...newList]);
      // 获取监测数据
      getDataList(selectedEquipId);
    } else {
      // 如果没有监测指标，只显示时间列
      setFilterCol([...fixedCol]);
    }
  };

  /**
   * @description 根据选择公司获取设备
   */
  const getDeviceData = async () => {
    const { monitorType, formCompany } = form.getFieldsValue();
    const monitor = monitorType || '';
    const companyId = formCompany || '';
    if (!companyId) return;
    setResData([]);
    const data = await queryEquipInfoByCompanyId({
      companyId,
      type: monitorData,
      monitorOnline: '0',
      monitorType: monitor,
    });
    if (data.code === 200 && data.data instanceof Array && data.data.length > 0) {
      const deviceCode = data.data[0]?.code;
      form.setFieldsValue({ deviceCode });
      setDeviceDataList([...data.data]);
      // 设置选中的设备ID（这会触发useEffect自动调用getColData）
      const equipId = data.data.find((item: collistType) => item.code === deviceCode)?.id || '';
      setSelectedEquipId(equipId);
    } else {
      form.resetFields(['deviceCode']);
      setDeviceDataList([]);
      setSelectedEquipId('');
      setColumnList([]);
      setCheckValue([]);
    }
  };

  // 获取所属单位
  const getCompanyList = async () => {
    const data = await queryUnitInfoByType(monitorData);
    if (data.code === 200 && data.data instanceof Array && data.data.length > 0) {
      setCompanyList([...data.data]);
      form.setFieldsValue({ formCompany: data.data[0]?.companyId });
      getDeviceData();
    }
  };

  // 监听设备ID和采集频率变化，重新获取列数据
  useEffect(() => {
    if (selectedEquipId && dataSourceTypeKey) {
      getColData();
    }
  }, [selectedEquipId, dataSourceTypeKey]);

  useEffect(() => {
    if (open) {
      getCompanyList();
      getMonitorList();
      getFrequencyList();
    }
    return () => {
      setReportType('date');
      setFilterCol([...fixedCol]);
      setResData([]);
      setCheckValue([]);
      setColumnList([]);
      setDeviceDataList([]);
      setSelectedEquipId('');
      setDataSourceTypeKey('');
      setLoading(false);
    };
  }, [open]);

  useEffect(() => {
    // 创建Set去重
    const uniqueCodes = new Set(checkValue);
    // 根据选中的因子筛选列
    const newList = [];
    // 使用Set.has方法检查code是否存在
    columnList.forEach((item) => {
      if (uniqueCodes.has(item.code)) {
        newList.push(item);
      }
    });
    setFilterCol([...fixedCol, ...dealTreeData(newList)]);
  }, [JSON.stringify(checkValue)]);

  // 文件导出
  const uploadTemplate = async () => {
    const { startDate, endDate, dataSourceType, formCompany, deviceCode, monitorType } =
      form.getFieldsValue();

    // 检查时间范围限制
    if (checkDateRangeLimit(startDate, endDate, dataSourceType)) {
      return; // 超过限制，不执行导出
    }

    setLoading(true);
    const equipData = deviceDataList.find((device) => device.code === deviceCode);
    const { newStart, newEnd } = changeTimeFormat(reportType, dataSourceType, startDate, endDate);
    const companyName = companyList.find((str) => str.companyId === formCompany)?.supplyUnit || '-';

    if (!equipData) {
      message.error('请选择设备名称');
      setLoading(false);
      return;
    }

    const newList = [...filterCol];
    newList.shift();
    const params = {
      companyName,
      equipName: equipData?.name ?? '',
      equipCd: equipData?.code ?? '',
      equipId: equipData?.id ?? '',
      orgCd: formCompany || '',
      startTm: newStart,
      endTm: newEnd,
      dateType: reportType === 'month' ? '4' : dataSourceType,
      monitorType,
      reportType: reportTypeData.find((r) => r.code === reportType).key,
      monitorIndexHeaderParamList: newList,
      frequency: dataSourceTypeKey,
    };
    const exportData = await exportManualFillData({
      data: params,
      responseType: 'formData',
    });
    setLoading(false);
    const href = window.URL.createObjectURL(
      new Blob([exportData], { type: 'application/vnd.ms-excel;charset=utf-8' }),
    );
    const link = document.createElement('a');
    link.href = href;
    link.download =
      companyName +
      equipData.name +
      (exportType === 'envQuality' ? '环境质量监测报表' : '污染源监测报表');
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
    window.URL.revokeObjectURL(href);
  };

  /**
   * @description Popover弹出内容content
   */
  const popoverPontent: React.JSX.Element = (
    <div
      style={{
        maxHeight: 300,
        width: 150,
        overflowY: 'auto',
      }}
    >
      {columnList.length >= 1 ? (
        <Checkbox.Group
          value={checkValue}
          onChange={(e) => {
            if (e.length === 0) return;
            setCheckValue(e as string[]);
          }}
        >
          <Row>
            {columnList.map((item) => {
              return (
                <Col key={item.code} span={24}>
                  <Checkbox value={item.code}>{item.name}</Checkbox>
                </Col>
              );
            })}
          </Row>
        </Checkbox.Group>
      ) : (
        <Empty />
      )}
    </div>
  );

  const onCloseModal = () => {
    form.resetFields();
    onResult();
  };

  /**
   * 获取当前应该使用的日期选择器类型
   */
  const getPickerType = (): 'date' | 'month' | 'year' => {
    if (reportType === 'month') {
      return 'month';
    }
    if (dataSourceTypeKey === '9999') {
      return 'year';
    }
    return 'date';
  };

  /**
   * 获取当前应该使用的日期格式
   */
  const getDateFormat = (): string => {
    // 默认格式
    if (dataSourceTypeKey === '2011') {
      return 'YYYY-MM-DD HH:mm:ss'; // 实时数据：到秒
    }
    if (dataSourceTypeKey === '2061') {
      return 'YYYY-MM-DD HH'; // 时均数据：到小时
    }
    if (dataSourceTypeKey === '2031') {
      return 'YYYY-MM-DD'; // 日均数据：到日
    }
    if (dataSourceTypeKey === '9999') {
      return 'YYYY'; // 年均数据：到年
    }
    return 'YYYY-MM-DD';
  };

  /**
   * 获取时间选择器的配置
   */
  const getShowTimeConfig = () => {
    if (dataSourceTypeKey === '2011') {
      // 实时数据：显示时分秒
      return { format: 'HH:mm:ss' };
    }
    if (dataSourceTypeKey === '2061') {
      // 时均数据：只显示小时，分钟秒钟设为00
      return {
        format: 'HH',
        hideDisabledOptions: true,
        defaultValue: moment().startOf('hour'),
      };
    }
    return false;
  };

  const disabledStartDate = (current) => {
    const { endDate } = form.getFieldsValue();
    return current && current > endDate;
  };

  const disabledEndDate = (current) => {
    const { startDate } = form.getFieldsValue();
    return current && current < startDate;
  };

  return (
    <div>
      <Modal
        title="报表"
        visible={open}
        key="exportReportModl"
        closable
        width="80%"
        onCancel={onCloseModal}
        footer={null}
        className={style['manual-export-modal']}
        destroyOnClose
      >
        <Form
          form={form}
          name="exportReportForm"
          className={style['ant-gasleak-form']}
          onFinish={() => getDataList()}
          initialValues={{
            formReport: reportType,
            startDate: moment(),
            endDate: moment(),
          }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="formCompany"
                label="所属单位"
                rules={[{ required: true, message: '请选择所属单位' }]}
              >
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getDeviceData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getDeviceData();
                  }}
                >
                  {(companyList || []).map((item) => (
                    <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="monitorType" label="监测类型">
                <Select
                  placeholder="请选择"
                  onChange={() => {
                    form.resetFields(['deviceCode']);
                    getDeviceData();
                  }}
                >
                  {monitorList?.map((item) => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="deviceCode"
                label="设备名称"
                rules={[{ required: true, message: '请选择设备名称' }]}
              >
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={(deviceCode) => {
                    // 更新选中的设备ID
                    const equipId =
                      deviceDataList.find((item) => item.code === deviceCode)?.id || '';
                    setSelectedEquipId(equipId);
                  }}
                >
                  {(deviceDataList || []).map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* <Col span={6}>
              <Form.Item name="formReport" label="类型">
                <Select
                  placeholder="请选择"
                  onChange={(value) => {
                    setReportType(value);
                  }}
                >
                  {reportTypeData.map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col> */}
            {reportType === 'date' && (
              <Col span={6}>
                <Form.Item name="dataSourceType" label="采集频率">
                  <Select
                    placeholder="请选择"
                    onSelect={(e) => {
                      setDataSourceTypeKey(e);

                      // 获取新的默认时间范围
                      const newTimeRange = getDefaultTimeRange(e);
                      form.setFieldsValue({
                        startDate: newTimeRange.startDate,
                        endDate: newTimeRange.endDate,
                      });

                      // 实时数据、时均数据、日均数据都需要展示范围选择器，只有年均数据不需要
                      if (e === '9999') {
                        setShowEnd(false);
                      } else {
                        setShowEnd(true);
                      }
                    }}
                  >
                    {frequencyList.map((item) => (
                      <Select.Option key={item.remark}>{item.text}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
            <Col span={10} style={{ display: 'flex' }}>
              <Form.Item name="startDate" label="监测时间">
                <DatePicker
                  style={{ width: '100%' }}
                  picker={getPickerType()}
                  showTime={getShowTimeConfig()}
                  format={getDateFormat()}
                  disabledDate={disabledStartDate}
                />
              </Form.Item>
              {reportType === 'date' && showEnd && (
                <Form.Item name="endDate" label="至">
                  <DatePicker
                    style={{ width: '100%' }}
                    picker={getPickerType()}
                    showTime={getShowTimeConfig()}
                    format={getDateFormat()}
                    disabledDate={disabledEndDate}
                  />
                </Form.Item>
              )}
            </Col>
            <Col span={14} style={{ textAlign: 'end' }}>
              <Button className={style['top-button']} type="primary" htmlType="submit">
                查询
              </Button>
              <Button
                style={{ marginLeft: 15 }}
                className={style['top-button']}
                type="primary"
                onClick={uploadTemplate}
              >
                导出Excel
              </Button>
            </Col>
          </Row>
        </Form>

        <div className={style['export-manual-popover']}>
          <Popover
            placement="bottomRight"
            overlayClassName={style['export-popover-content']}
            trigger="click"
            title={null}
            content={popoverPontent}
          >
            <Button type="primary" size="small" ghost>
              因子配置
            </Button>
          </Popover>
        </div>
        <Table
          loading={listloading || loading}
          bordered
          rowKey={(row) => row.id}
          dataSource={resData}
          className={style['export-table']}
          rowClassName={(_, index) => {
            if (index % 2 === 0) {
              return style['even-row'];
            }
            return style['odd-row'];
          }}
          columns={filterCol}
          scroll={{ x: checkValue.length * 180, y: 450 }}
        />
      </Modal>
    </div>
  );
};
export default exportReportModl;
