import React, { useCallback, useEffect, useRef, useState } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { message, Modal } from 'antd';
import locales from '@/locales';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { CurrentUser } from '@/Constant';
import { queryOrganizationTreeListByLoginUser } from '@/service/baseModuleApi';
import { queryEnvAlarmPage, ResponsPageType } from '@/service/envApi';
import style from './home.module.less';

type propsTypes = {
  open: boolean;
  onClose: () => void;
  itemData: {
    key: string;
    name: string;
  };
  /** 判断是否是园区账号 */
  isPark: boolean;
  selectCompany: {
    label: string;
    value: string;
  };
};

type filterType = {
  type?: string;
  equipNm?: string;
  supplyUnit?: { code: string; text: string }[];
  description?: string;
  indexNm?: string;
  frequency?: { code: string; text: string }[];
  equipState?: { code: string; text: string }[];
};

type filterTypeData = {
  equipNm?: string;
  supplyUnit?: string;
  description?: string;
  indexNm?: string;
  frequency?: string;
  equipState?: string;
  type?: string;
  disposeState?: number;
};

/**
 * @description 报警信息列表
 */
const AlarmModalView: React.FC<propsTypes> = (props) => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const { open, onClose, itemData, isPark, selectCompany } = props;
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  useEffect(() => {
    setIsModalOpen(open);
  }, [open]);

  const handleFilter: (f: filterType) => filterTypeData = (f) => {
    const filter: filterTypeData = {
      type: itemData.key,
      equipNm: f.equipNm || '',
      supplyUnit: isPark
        ? (f?.supplyUnit && f?.supplyUnit[0]?.text) || ''
        : CurrentUser()?.unitName,
      description: f.description || '',
      indexNm: f.indexNm || '',
      disposeState: 0,
      equipState: f.equipState && f.equipState.length > 0 ? f.equipState[0].code : undefined,
    };
    return filter;
  };

  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'supplyUnit',
      title: '所属单位',
      width: 200,
      query: isPark,
      display: isPark,
      fixed: 'left',
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const data: Record<string, string>[] = await queryOrganizationTreeListByLoginUser();
          const plOp: { text: string; code: string }[] = [];
          if (data instanceof Array) {
            data.forEach((item: Record<string, string>) => {
              plOp.push({
                code: item.unitCode,
                text: item.unitName,
              });
            });
          }
          return plOp;
        },
        p_props: {
          changeOnSelect: true,
          placeholder: '请选择',
        },
      },
    },
    {
      dataIndex: 'equipNm',
      title: '设备名称',
      width: 180,
      query: true,
      display: true,
    },
    {
      dataIndex: 'equipCd',
      title: '设备编号',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'equipState',
      title: '设备状态',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '请选择',
        },
        request: () => {
          return [
            { code: '0', text: '离线' },
            { code: '1', text: '在线' },
          ];
        },
      },
      render: (value) => {
        if (value === '0') {
          return <div>离线</div>;
        }
        if (value === '1') {
          return <div>在线</div>;
        }
        return <div> - </div>;
      },
    },
    {
      dataIndex: 'description',
      title: '监测对象',
      width: 180,
      query: true,
      display: true,
    },

    {
      dataIndex: 'indexNm',
      title: '监测指标名称',
      width: 180,
      query: true,
      display: true,
    },
    {
      dataIndex: 'measureUnit',
      title: '计量单位',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'currVal',
      title: '当前值',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'firstLevelMax',
      title: '一级阈值上限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'firstLevelMin',
      title: '一级阈值下限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'secondLevelMax',
      title: '二级阈值上限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'secondLevelMin',
      title: '二级阈值下限',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'alarmTime',
      title: '报警开始时间',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'alarmLevelText',
      title: '报警级别',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'maxRealtimeValLongitude',
      title: '最大实时值的经度',
      width: 180,
      query: false,
      display: false,
    },
    {
      dataIndex: 'maxRealtimeValLatitude',
      title: '最大实时值的纬度',
      width: 180,
      query: false,
      display: false,
    },
    {
      dataIndex: 'disposeState',
      title: '处置情况',
      width: 180,
      query: false,
      display: true,
      render: (value) => {
        if (value === '0' || value === 0) {
          return <span style={{ color: 'red' }}>未处置</span>;
        }
        if (value === '1' || value === 1) {
          return <span style={{ color: 'green' }}>已处置</span>;
        }
        return <div> - </div>;
      },
    },
    {
      dataIndex: 'disposeTm',
      title: '处置时间',
      width: 180,
      query: false,
      display: true,
    },
  ];

  const listRender: () => JSX.Element = useCallback(() => {
    return (
      <YTHList
        code="alarmModalViewList"
        action={aa}
        // searchMemory={true}
        defaultQuery={{
          supplyUnit: selectCompany?.value !== 'all' && [
            { text: selectCompany?.label, code: selectCompany?.value },
          ],
        }}
        actionRef={ref}
        showRowSelection={false}
        operation={[]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ResponsPageType = await queryEnvAlarmPage({
            aescs: [],
            descs: [],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            /*
             */
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          message.error('请求数据出错，请刷新重试或联系管理员');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={150}
        columns={columns}
      />
    );
  }, [selectCompany, itemData]);

  return (
    <Modal
      title={`${itemData?.name}报警列表`}
      visible={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
        if (onClose) onClose();
      }}
      destroyOnClose
      className={style['alarm-modal']}
      width="80%"
      footer={null}
    >
      {listRender()}
    </Modal>
  );
};

export default YTHLocalization.withLocal(AlarmModalView, locales, YTHLocalization.getLanguage());
