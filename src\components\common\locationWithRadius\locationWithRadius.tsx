import React, { useState, useEffect, useRef } from 'react';

import { Button, Input, Modal, Spin, message } from 'antd';
import dicParams from '@/utils/dicParams';
import style from './locationWithRadius.module.less';
import MapCard from '../MapCard';

const LocationWithRadius: React.FC<any> = ({
  operateType, // edit add view draw drawpolygon
  radius, // 覆盖半径
  value = '', // value 就是form item的initialValue和value
  confirmClick = () => {}, // 确认选点
  drawConfirm = () => {}, // 确认画多边形
  onChange = () => {}, // onChange 事件
}) => {
  // const MAP_KEY = dicParams.mapKey;
  const [isShowAddObjectModal, setIsShowAddObjectModal] = useState(false); // 标记是否展示新增对象弹窗

  const [, setmapselectCoord] = useState<any>([]); // 设置地图选点选中
  // const [loading, setLoading] = useState<boolean>(false); // 是否加载
  const [position, setPosition] = useState<any>(dicParams.mapCenterObj); // 地图中心点
  // 地图中心变化
  const [inputValue, setInputValue] = useState<string>(''); // input 的值
  // const [polygonList, setPolygonList] = useState<any>('');
  const [isDrawing, setIsDrawing] = useState<boolean>(false); // 标记正在绘制地图
  const [radiusValue, setRadiusValue] = useState<number>(0); //  暂存覆盖范围
  const tmapRef = useRef<any>();
  useEffect(() => {
    /// /console.log(" --- ", value, locationLabel);
    setInputValue(value);
    if (value && value !== '') {
      // 根据传过来的坐标点以及模式（draw 手绘制多边形 drawpolygon 边界预览 view 普通点位预览 add 新增编辑点位）
      if (operateType.includes('draw')) {
        // setPolygonList(value);
        const data = JSON.parse(value);
        if (data.length > 0) {
          setPosition([data[0][0].lng, data[0][0].lat]);
        }
      } else {
        const locationList = String(value).split(',');
        /// /console.log(locationList);
        if (locationList.length === 2) {
          setPosition({
            x: parseFloat(locationList[0]),
            y: parseFloat(locationList[1]),
            z: 0,
          });
          // alert(radius);
          if (radiusValue === 0) {
            setRadiusValue(radius);
          }
        } else {
          setPosition([0, 0]);
          // setPosition([parseFloat(locationList[0]), parseFloat(locationList[1])]);
        }
      }
    } else if (operateType === 'view') {
      // 没有坐标数据
      // 如果模式是查看，那么设置地图中心店为0 不能展示
      setPosition([0, 0]);
    }
  }, [value]);

  const draw = (isFirst: any) => {
    tmapRef.current.drawPolygon(isFirst);
  };

  const clearPolygons = () => {
    tmapRef.current.clearPolygons();
    setIsDrawing(false);
    draw(true);
  };

  // 关闭弹窗
  const closeAddModal = () => {
    // //console.log('调用了关闭modal')
    setIsDrawing(false);
    setIsShowAddObjectModal(false);
  };

  const save = () => {
    const polygons = tmapRef.current.getDrawedPolygons();
    /// /console.log(JSON.stringify(polygons));
    drawConfirm(JSON.stringify(polygons));
    closeAddModal();
  };

  const confirmLocationSelect = () => {
    const radiusVal = tmapRef.current.getMapCenterAndRadius();

    confirmClick(radiusVal);
    setRadiusValue(radiusVal.r);
    onChange?.(`${radiusVal.x},${radiusVal.y}`);

    setIsShowAddObjectModal(false);
  };
  const showMap = () => {
    /// /console.log("loc", position, operateType);
    if (operateType === 'view' && position[0] === 0 && position[1] === 0) {
      message.info('无坐标数据');
    } else {
      setIsShowAddObjectModal(true);
    }
  };
  const onCoordChange = (val: any) => {
    setmapselectCoord(val);
  };

  return (
    <div className={style['location-select-container']}>
      <Input disabled className={style['map-select-input']} value={inputValue} />

      <Button className={style['map-select-btn']} type="primary" size="small" onClick={showMap}>
        {operateType === 'add' || operateType === 'edit' || operateType === 'draw'
          ? '地图拾取'
          : '查看'}
      </Button>

      <Modal
        title="标绘位置"
        width="60vw"
        destroyOnClose
        visible={isShowAddObjectModal}
        footer={null}
        onCancel={closeAddModal}
        maskClosable={false}
      >
        <Spin spinning={false}>
          <div className={style['map-locationradiusmodal-content']}>
            <div className={style['map-area']}>
              <MapCard
                onRef={tmapRef}
                onCoordChange={onCoordChange}
                mapConfig={{ satellite: true }}
                position={position}
                radius={radiusValue}
                operateType={operateType}
              />
            </div>
            {/* <div className="tips">
              注：默认获取当前位置，支持地点检索；点击地图具体位置即可获取经纬度坐标
            </div> */}
            <div className={style.footer}>
              <Button className={style['location-selector-cancel-btn']} onClick={closeAddModal}>
                取消
              </Button>
              {operateType === 'draw' && (
                <div>
                  <Button
                    className={style['confirm-btn']}
                    type="primary"
                    onClick={() => {
                      if (!isDrawing) {
                        draw(true);
                        setIsDrawing(true);
                      } else {
                        message.info('开始绘制');
                      }
                    }}
                  >
                    绘制企业位置
                  </Button>
                  <Button
                    className={style['confirm-btn']}
                    type="primary"
                    onClick={() => {
                      clearPolygons();
                    }}
                  >
                    清空重绘
                  </Button>
                  <Button
                    className={style['confirm-btn']}
                    type="primary"
                    onClick={() => {
                      save();
                    }}
                  >
                    保存
                  </Button>
                </div>
              )}
              {(operateType === 'add' || operateType === 'edit') && (
                <Button
                  className={style['confirm-btn']}
                  onClick={confirmLocationSelect}
                  type="primary"
                >
                  确定
                </Button>
              )}
            </div>
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

export default LocationWithRadius;
