{
  "alias": {
    "@": "./src"
  },
  "devServer": {
    "hot": true,
    "disableHostCheck": true,
    "clientLogLevel": "silent",
    "port": 30351,
    "proxy": {
      "/gw": {
        "target": "http://************:30733/gw",
        "pathRewrite": {
          "^/gw": ""
        },
        "changeOrigin": true
      },
      "/api": {
        "target": "http://*************:50006",
        "pathRewrite": {
          "^/api": ""
        },
        "changeOrigin": true
      }
    }
  },
  "minify": true,
  "plugins": [
    [
      "build-plugin-component",
      {
        "subComponents": true
      }
    ],
    ["./build.plugin.js"],
    [
      "build-plugin-stark-module",
      {
        "modules": {
          "test": "./src/test/index.tsx",
          // "EnvQualityOnLineMonitorList": "./src/pages/baseInfo/EnvQualityMonitorList.tsx",
          // "PollutionSourceOnLineMonitorList": "./src/pages/baseInfo/PollutionSourceMonitorList.tsx",
          // "EnvQualityManualFillList": "./src/pages/manualFill/EnvQualityManualFillList.tsx",
          // "PollutionSourceManualFillList": "./src/pages/manualFill/PollutionSourceManualFillList.tsx",
          // "EnvQualityList": "./src/pages/onLineMonitorNew/EnvQualityList.tsx",
          // "WasteGasList": "./src/pages/onLineMonitorNew/PollutionSourceList.tsx",
          // "EnvAirAlarmList": "./src/pages/envAlarm/EnvAirAlarmList.tsx",
          // "PolluteAlarmList": "./src/pages/envAlarm/PolluteAlarmList.tsx",
          // "StatisticalIndex": "./src/pages/statistical/index.tsx",
          // "StatisticalReportList": "./src/pages/statistical/ReportList.tsx",
          "EnvHomePage": "./src/pages/homePagePreview/index.tsx"
        },
        "filenameStrategy": "[name]",
        "moduleExternals": {
          "react": {
            "root": "React",
            "url": "https://g.alicdn.com/code/lib/react/17.0.2/umd/react.production.min.js"
          },
          "react-dom": {
            "root": "ReactDOM",
            "url": "https://g.alicdn.com/code/lib/react-dom/17.0.2/umd/react-dom.production.min.js"
          },
          "antd": {
            "root": "antd",
            "url": "https://unpkg.com/antd/dist/antd.min.js"
          },
          "moment": {
            "root": "moment",
            "url": "https://cdn.jsdelivr.net/npm/moment@2.29.3/min/moment-with-locales.min.js"
          },
          "@antv/x6": {
            "root": "X6",
            "url": "https://unpkg.com/@antv/x6/dist/x6.js"
          }
        }
      }
    ]
  ]
}
