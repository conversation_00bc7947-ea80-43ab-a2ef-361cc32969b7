import React from 'react';
import OnlineMonitor from './index';

/**
 * @description 环境质量监测页面示例
 * 使用通用在线监测组件，传入'envQuality'类型
 * 该类型对应环境质量监测，使用A22A08A06字典值和Air监测类型
 * @returns {React.ReactElement} 环境质量在线监测页面
 */
export const EnvQualityMonitorPage: React.FC = () => {
  return <OnlineMonitor monitorConfigType="envQuality" />;
};

/**
 * @description 污染源监测页面示例
 * 使用通用在线监测组件，传入'wasteGas'类型
 * 该类型对应污染源监测，使用A22A08A07字典值和wasteAir监测类型
 * 会过滤掉A22A08A07A05监测类型
 * @returns {React.ReactElement} 污染源在线监测页面
 */
export const WasteGasMonitorPage: React.FC = () => {
  return <OnlineMonitor monitorConfigType="wasteGas" />;
};

/**
 * @description 公用工程监测页面示例
 * 使用通用在线监测组件，传入'utilityMonitor'类型
 * 该类型对应公用工程监测，使用A22A08A03字典值和wasteAir监测类型
 * 只显示A22A08A07A05监测类型
 * @returns {React.ReactElement} 公用工程在线监测页面
 */
export const UtilityMonitorPage: React.FC = () => {
  return <OnlineMonitor monitorConfigType="utilityMonitor" />;
};

/**
 * @description 自定义配置的监测页面示例
 * 使用通用在线监测组件，传入'envQuality'类型并覆盖部分配置
 * 可以通过customConfig参数覆盖默认配置
 * 这里修改了导出报表名称
 * @returns {React.ReactElement} 自定义在线监测页面
 */
export const CustomMonitorPage: React.FC = () => {
  return (
    <OnlineMonitor
      monitorConfigType="envQuality"
      customConfig={{
        exportName: '自定义监测报表',
      }}
    />
  );
};
