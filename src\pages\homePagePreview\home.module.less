.map-center {
  width: 100%;
  height: 100%;
}

.map-top-conten {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
}

.form-picker {
  :global {
    .ant-input,
    .ant-form-item-label > label,
    .ant-select-selector,
    .ant-picker,
    .ant-select-item-option-content,
    .ant-btn {
      font-size: 12px;
    }
  }
}

.even-row {
  & > td {
    background-color: #fff;
  }
}

.odd-row {
  & > td {
    background-color: #f8f9fa;
  }
}

.modal-device-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;

  .modal-device-info:global(.ant-table-thead > tr > th) {
    padding: 10px 16px;
    font-size: 12px;
    font-weight: bold;
    height: 40px;
    background-color: #f8f9fa;
  }

  .modal-device-info:global(.ant-table-tbody > tr > td) {
    height: 32px;
    font-size: 12px !important;
    padding: 0 16px !important;
    border-color: rgb(244 244 244) !important;
    line-height: 32px !important;
    border-bottom-width: 1px !important;
    border-bottom-style: solid !important;
    border-right-width: 1px !important;
    border-right-style: solid !important;
  }

  .modal-device-info:global(.ant-table-tbody > tr:hover > td) {
    background-color: #ecf6ff !important;
  }

  .modal-device-info-content {
    display: flex;
    justify-content: space-between;
  }

  .content-title {
    font-weight: bold;
  }

  .content-value {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.device-table {
  :global {
    .ant-table-thead > tr > th {
      padding: 10px 16px;
      font-size: 12px;
      font-weight: bold;
      height: 40px;
      background-color: #f8f9fa;
    }

    .ant-table-tbody > tr > td {
      height: 32px;
      font-size: 12px !important;
      padding: 0 16px !important;
      border-color: rgb(244 244 244) !important;
      line-height: 32px !important;
      border-bottom-width: 1px !important;
      border-bottom-style: solid !important;
      border-right-width: 1px !important;
      border-right-style: solid !important;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #ecf6ff !important;
    }
  }
}

.device-online {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 230px;
  height: 333px;
  background-color: #fff;
  border-radius: 3px;

  .header {
    display: flex;
    align-items: center;
    height: 30px;
    background-color: #eee;
    border-radius: 3px;

    > span {
      padding: 10px;
      font-family: PingFangSC, 'PingFang SC', sans-serif;
      font-weight: 500;
      font-style: normal;
      color: #000;
      line-height: 13px;
      text-align: center;
    }
  }

  .device-content-echarts {
    height: 100px;
    padding: 10px 0;
  }

  .device-content {
    display: flex;
    justify-content: space-around;

    .device-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100px;
      cursor: pointer;

      .device-item-num {
        font-size: 18px;
        color: #464646;
        line-height: 18px;
      }

      .device-item-name {
        font-size: 10px;
        color: #626262;
      }
    }
  }

  .first-contetn {
    padding-top: 10px;
  }
}

.alarm-situation {
  position: absolute;
  top: 355px;
  right: 10px;
  width: 230px;
  background-color: #fff;
  border-radius: 3px;

  .header {
    display: flex;
    align-items: center;
    height: 30px;
    margin-bottom: 10px;
    background-color: #eee;
    border-radius: 3px;

    > span {
      padding: 10px;
      font-family: PingFangSC, 'PingFang SC', sans-serif;
      font-weight: 500;
      font-style: normal;
      color: #000;
      line-height: 13px;
      text-align: center;
    }
  }

  .alarm-constent {
    padding: 0 10px;

    .alarm-item {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 55px;
      margin-bottom: 8px;
      background-color: rgb(255 245 245 / 77%);
      cursor: pointer;

      > div {
        flex: 1;
      }

      .alarm-title {
        display: flex;
        flex-direction: column;
        align-items: center;

        .alarm-num {
          font-size: 18px;
          color: #f4482d;
        }

        .alarm-name {
          font-size: 12px;
          color: #898989;
        }
      }

      .alarm-img {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.device-modal {
  .ant-modal-body {
    background-color: #eee;
  }
}

.modal-monitor {
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
}

.table-columns-title {
  width: 180px;
  padding: 0 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.hidden {
  transform: translateX(312px);
}

.alarm-modal {
  :global{
		.ant-modal-body {
			height: 600px !important;
			padding: 0;
			background-color: #eee;
		}
	}
}

.icon-display {
  position: absolute;
  top: 10px;
  left: 50%;
  z-index: 999;
  display: flex;
  transform: translate(-50%, 0);

  .icon-btn {
    margin: 0 10px;
    padding: 2px 5px;
    font-size: 12px;
    color: rgb(0 0 0 / 85%);
    border: 1px solid #d9d9d9;
    background-color: #f7f7f7;
    border-radius: 3px;
    cursor: pointer;
  }

  .icon-btn-selected {
    margin: 0 10px;
    padding: 2px 5px;
    font-size: 12px;
    color: #4096ff;
    background-color: #ebf5ff;
    border: 1px solid #4096ff;
    border-radius: 3px;
    cursor: pointer;
  }
}

.popover-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 0;
  cursor: pointer;

  .popover-name {
    flex: 4;
  }

  .popover-checkbox {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    margin-top: 1px;
  }
}
