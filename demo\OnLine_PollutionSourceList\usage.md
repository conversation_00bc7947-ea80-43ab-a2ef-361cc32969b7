---
title: 在线监测
order: 1
---
污染源监测

```jsx
import React, { Component } from 'react';
import ReactDOM from 'react-dom';
// import PollutionSourceList from '@/pages/onLineMonitor/WasteGasList';
import PollutionSourceList from '@/pages/onLineMonitorNew/PollutionSourceList';

class App extends Component {
  render() {
    return (
      <div>
        <PollutionSourceList />
      </div>
    );
  }
}

ReactDOM.render(<App />, mountNode);
```
