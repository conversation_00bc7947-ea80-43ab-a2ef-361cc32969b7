apiVersion: v1
kind: Service
metadata:
  labels:
    app: CIP
    component: campus-front-environment
  name: campus-front-environment
  namespace: campus
spec:
  ports:
    - name: http-80
      port: 80
      protocol: TCP
      targetPort: 80
      #30000-32767
      nodePort: 30015
  selector:
    app: CIP
    component: campus-front-environment
    tier: backend
  sessionAffinity: None
  type: NodePort
