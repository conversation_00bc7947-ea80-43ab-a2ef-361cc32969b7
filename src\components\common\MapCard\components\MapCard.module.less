.spin-wrap {
	width: 100%;
	height: 100%;

	:global {
		.ant-spin-container {
			width: 100%;
			height: 100%;
		}
	}
}

.cesium-viewer-fullscreen-container {
	display: none !important;
}

.ant-card-body {
	padding-top: 0;
}

.map-container {
	max-height: 600px;
	height: 100%;
	width: 100%;
	position: relative;

	.toolbar {
		display: flex;
		place-content: flex-start space-between;
		padding: 10px;
		flex-wrap: wrap;
		z-index: 1;
		position: absolute;
		pointer-events: none;
		top: 0;
		left: 0;
		width: 80%;

		.result-panel {
			width: 320px;
			max-height: 350px;
			display: flex;
			flex-direction: column;
			margin-bottom: 10px;
			pointer-events: all;

			&.ant-card {
				background-color: rgb(255 255 255 / 60%);

				.ant-select-selector {
					background-color: rgb(255 255 255 / 70%);
				}

				.ant-card-head {
					padding: 0 12px;
				}

				.ant-card-head-title {
					padding: 12px 0;
				}
			}

			.search-bar {
				display: flex;
				align-items: center;
				margin-right: 10px;

				.text {
					text-overflow: ellipsis;
					flex: 1;
					overflow: hidden;
					white-space: nowrap;
				}
			}

			.result-list.ant-list-loading {
				min-height: 100px;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}

		.info {
			padding: 12px 12px 0;
			background-color: rgb(255 255 255 / 60%);
			border-radius: 2px;
			pointer-events: all;

			.item {
				margin-bottom: 12px;
				display: flex;
				align-items: center;

				.search-bar {
					margin-right: 10px;
				}
			}

			.ant-input {
				background-color: rgb(255 255 255 / 70%);
			}

			.address-text {
				margin-bottom: 0;
				color: rgb(0 0 0 / 85%);
				max-width: 210px;
			}

			input[type="number"] {
				appearance: textfield;
			}

			input[type="number"]::-webkit-inner-spin-button,
			input[type="number"]::-webkit-outer-spin-button {
				margin: 0;
				appearance: none;
			}
		}
	}
}
