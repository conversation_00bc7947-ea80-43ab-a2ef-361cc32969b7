import React, { useEffect, useState } from 'react';
import {
  Modal,
  message,
  Button,
  Popover,
  Empty,
  Checkbox,
  Table,
  Form,
  Select,
  DatePicker,
  Row,
  Col,
  Divider,
} from 'antd';
import moment from 'moment';
import type { IYTHColumnProps } from 'yth-ui/es/components/list/index';
import formApi from '@/service/formApi';
import {
  queryMonitorValueDayReport,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  exportMonitorData,
} from '@/service/envApi';
import style from './index.module.less';

type objType = Record<string, string>;

/**
 * @description 时间类型
 * @description hour：时均值
 * @description daily：日均值
 * @description realTime：实时数据
 */
type DateTypes = 'hour' | 'daily' | 'realTime';
/**
 * 存储的查询条件 用于报表和统计图默认查询条件
 */
type CacheTypes = {
  /** 时间类型 */
  sourceType?: DateTypes;
  /** 监测时间 */
  rangeDate?: moment.Moment;
};

type PropsTypes = {
  open: boolean;
  onResult?: (result?: objType) => void;
  /** 父级传入的表头list数据 */
  columnsData?: objType[];
  /** 父级传入已选择的所属单位数据companyId 单位的code值 */
  companyData?: string;
  /** 父级传入已选择设备数据 */
  deviceData?: string | objType;
  /** 父级传入时间和数据源数据 */
  cacheData: CacheTypes;
  /** 父级传入可选择设备list数据 */
  deviceList: objType[];
  /** 父级传入可选择所属单位list数据 */
  companyList: objType[];
  monitorKey?: 'A22A08A06' | 'A22A08A07' | 'A22A08A01' | 'A22A08A03';
  /** 父级传入的已选择监测类型数据  */
  monitorType?: string;
  /** 监测类型 */
  envType: 'Air' | 'harm' | 'wasteAir';
  exportName: string;
};

/**
 * @description 报表类型types
 */
type ReportTypes = {
  name: '日报表' | '月报表';
  code: 'date' | 'month';
  key: string;
};

/**
 * @description 数据源types
 */
type SourceType = {
  name: '小时数据' | '日数据' | '实时数据';
  code: 'hour' | 'daily' | 'realTime';
  key: string;
  format: string;
};

/**
 * table 最后一页增加以下行数据
 */
const lastCol = {
  cou: '个数',
  min: '最小值',
  max: '最大值',
  mean: '均值',
  total: '合计',
};

/**
 * 均值和累计值数据标识
 */
const dataformat = [
  { value: '均值', key: 'avg' },
  { value: '累计值', key: 'sum' },
];

/**
 * 报表类型
 */
const reportTypeData: ReportTypes[] = [
  { name: '日报表', code: 'date', key: '1' },
  { name: '月报表', code: 'month', key: '2' },
];

// 数据源
const dateList: SourceType[] = [
  { name: '小时数据', code: 'hour', key: '2', format: 'YYYY-MM-DD HH' },
  { name: '日数据', code: 'daily', key: '3', format: 'YYYY-MM-DD' },
  { name: '实时数据', code: 'realTime', key: '1', format: 'YYYY-MM-DD HH:mm:ss' },
];

/**
 * @description 导出modal
 * @param param0
 * @returns
 */
const exportReportModl: React.FC<PropsTypes> = ({
  open,
  onResult,
  columnsData,
  companyData,
  deviceData,
  cacheData,
  deviceList,
  monitorKey,
  monitorType,
  envType,
  companyList,
  exportName,
}) => {
  const [form] = Form.useForm();
  const [checkValue, setCheckValue] = useState<React.Key[]>([]); // 因子配置选择的可见表头项
  const [columnList, setColumnList] = useState<objType[]>([]); // 原始column表头数据
  const [filterCol, setFilterCol] = useState<IYTHColumnProps[]>([]); // 根据因子配置筛选的column数据
  const [resData, setResData] = useState<objType[]>([]); // 列表返回数据
  const [loading, setLoading] = useState<boolean>(false);
  const [reportType, setReportType] = useState<ReportTypes['code']>('date'); // 报表类型
  const [showEnd, setShowEnd] = useState<boolean>(false); // 根据条件判断是否展示结束时间
  const [deviceDataList, setDeviceDataList] = useState<objType[]>([]); // 设备数据list
  const [monitorList, setMonitorList] = useState<objType[]>([]); // 监测类型数据

  const getMonitorList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: monitorKey === 'A22A08A03' ? 'A22A08A07' : monitorKey, // 监测类型 污染源
      },
      currentPage: 0,
      pageSize: 0,
    });
    if (monitorKey === 'A22A08A03') {
      const newList = list?.filter((item) => item.code === 'A22A08A07A05') || [];
      setMonitorList([...newList]);
    } else if (monitorKey === 'A22A08A07') {
      const newList = list?.filter((item) => item.code !== 'A22A08A07A05') || [];
      setMonitorList([...newList]);
    } else {
      setMonitorList([...list]);
    }
  };

  const fixedCol: IYTHColumnProps[] = [
    {
      code: 'collectTime',
      name: '监测时间',
      width: 180,
      title: '监测时间',
      align: 'center',
      dataIndex: 'collectTime',
      sorter: true,
      // sorter: (a, b) => {
      //   return Number(moment(a.TIME).unix()) - Number(moment(b.TIME).unix());
      // },
      render: (_r, record) => {
        if (record.stat) {
          return lastCol[record.stat];
        }
        return record.TIME || '-';
      },
      fixed: 'left',
      key: 'collectTime',
    },
  ];

  /**
   * @description 格式化数据
   * @param treeData
   * @returns
   */
  const dealTreeData = (treeData: objType[]) => {
    const data: IYTHColumnProps[] = treeData.map((item, index) => {
      const newItem: IYTHColumnProps = {
        ...item,
        key: item.code + index,
        dataIndex: item.code + index,
        title: item.name,
        width: 180,
        align: 'center',
      };
      newItem.children = dataformat.map((str) => {
        return {
          key: item.code + str.key + index,
          dataIndex: item.code + str.key + index,
          align: 'center',
          title: str.value,
          children: [
            {
              key: item.code + str.key,
              dataIndex: item.code + str.key,
              align: 'center',
              title: item.measureUnit,
              render: (_r, record) => {
                const newVal = item.code + str.key;
                if (record[newVal]) {
                  if (record.stat) {
                    return record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-';
                  }
                  if (
                    item.firstLevelMax &&
                    item.firstLevelMin &&
                    (record[newVal] > item.firstLevelMax || record[newVal] < item.firstLevelMin)
                  ) {
                    return (
                      <div className={style.triangle}>
                        {record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-'}
                      </div>
                    );
                  }
                  return record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-';
                }
                if (record[newVal] === 0) {
                  return '0';
                }
                return '-';
              },
            },
          ],
        };
      });
      if (!item.isTotalizeValue || item.isTotalizeValue === '0') {
        delete newItem.children[1];
      }

      return newItem;
    });
    return data;
  };

  /**
   * @description 因子配置切换
   * @param list
   */
  const checkValueChange = (list: string[]) => {
    const newColList = [];
    list?.forEach((item) => {
      columnsData.forEach((str) => {
        if (item === str.code) {
          newColList.push({ ...str });
        }
      });
    });
    setFilterCol([...fixedCol, ...newColList]);
  };

  /**
   * @description 简单格式化时间
   * @param dataType // 报表类型
   * @param dataSource  // 数据源
   * @param startTime
   * @param endTime
   * @returns
   */
  const changeTimeFormat = (
    dataType: ReportTypes['code'],
    dataSource: SourceType['code'],
    startTime,
    endTime,
  ) => {
    let newStart = '';
    let newEnd = '';
    if (dataType === 'month') {
      newStart = moment(startTime).startOf('month').format('YYYY-MM-DD HH:mm:ss');
      newEnd = moment(startTime).endOf('month').format('YYYY-MM-DD HH:mm:ss');
    } else {
      switch (dataSource) {
        case 'daily':
          newStart = moment(startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          break;
        default:
          newStart = moment(startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(startTime).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          break;
      }
    }
    return { newStart, newEnd };
  };

  /**
   * @description 获取列表数据
   * @param list
   */
  const getDataList = async (params?: objType, id?: string) => {
    const { startDate, endDate, deviceCode, formCompany } = form.getFieldsValue();
    const newSourceType = form.getFieldValue('dataSourceType') || params.dataSourceType || '';
    const aaa = {
      dataType: reportType,
      dataSource: form.getFieldValue('dataSourceType') ?? '',
      startTime: startDate ?? params.startDate,
      endTime: endDate || params?.endDate || '',
    };
    const defaultId = deviceList.find((str) => str.code === deviceCode)?.id;

    const equipId =
      id || defaultId || deviceList.find((str) => str.code === deviceCode || deviceData)?.id;
    const { newStart, newEnd } = changeTimeFormat(
      aaa.dataType,
      aaa.dataSource,
      aaa.startTime,
      aaa.endTime,
    );
    setLoading(true);
    const resDatas = await queryMonitorValueDayReport({
      monitorType: envType,
      equipCd: deviceCode || deviceData,
      equipId,
      orgCd: formCompany || companyData,
      startTm: newStart,
      endTm: newEnd,
      dateType:
        reportType === 'month' ? '4' : dateList.find((str) => str.code === newSourceType)?.key,
    });
    if (resDatas.code && resDatas.code === 200 && resDatas.data) {
      setResData(Array.isArray(resDatas.data) ? [...resDatas.data] : []);
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
    }
    setLoading(false);
  };

  /**
   * @description 获取表头column数据 并更改结构
   */
  const getColData = async (list?: objType[]) => {
    const { deviceCode } = form.getFieldsValue();
    const dataList = list || deviceDataList || deviceList;
    const equipId = dataList.find((str) => str.code === deviceCode)?.id;
    setLoading(true);
    const data = await queryMonitorIndex(equipId);
    if (data.code === 200 && data.data.length > 0) {
      const newList = (data?.data.map((item) => item.code) as string[]) || [];
      const newData = (data.data as objType[]) || [];
      setColumnList([...newData]);
      setFilterCol([...fixedCol, ...dealTreeData(newData)]);
      setCheckValue([...newList]);
      getDataList(null, equipId);
    } else {
      setColumnList([]);
      setFilterCol([]);
      setCheckValue([]);
    }
    setLoading(false);
  };

  /**
   * @description 根据选择公司获取设备
   */
  const getDeviceData = async (companyId?: string) => {
    const { monitorType: newMonitorType, formCompany } = form.getFieldsValue();
    setLoading(true);
    setColumnList([]);
    setCheckValue([]);
    setFilterCol([...fixedCol]);
    const data = await queryEquipInfoByCompanyId({
      companyId: formCompany || companyId,
      type: monitorKey,
      monitorType: newMonitorType,
      monitorOnline: '1',
    });
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      if (deviceData) {
        form.setFieldsValue({ deviceCode: deviceData });
      } else {
        form.setFieldsValue({ deviceCode: data.data[0]?.code });
      }
      setDeviceDataList([...data.data]);
      getColData(data.data);
    } else {
      form.resetFields(['deviceCode']);
      setDeviceDataList([]);
      setColumnList([]);
      setCheckValue([]);
    }
    setLoading(false);
  };

  // 文件导出
  const uploadTemplate = async () => {
    setLoading(true);
    const { startDate, endDate, dataSourceType, formCompany, deviceCode } = form.getFieldsValue();
    const equipData = deviceDataList?.find((device) => device.code === deviceCode) || {};
    const { newStart, newEnd } = changeTimeFormat(reportType, dataSourceType, startDate, endDate);
    const newList = [...filterCol];
    const companyName = companyList.find((str) => str.companyId === formCompany)?.supplyUnit || '-';
    newList.shift();
    const params = {
      companyName,
      equipName: equipData.name ?? '',
      equipCd: equipData.code ?? '',
      equipId: equipData.id ?? '',
      orgCd: formCompany || '',
      startTm: newStart,
      endTm: newEnd,
      dateType:
        reportType === 'month'
          ? '4'
          : dateList.find((str) => str.code === dataSourceType)?.key || '',
      monitorType: envType,
      reportType: reportTypeData.find((r) => r.code === reportType).key,
      monitorIndexHeaderParamList: newList,
    };
    const responseData = await exportMonitorData({
      data: params,
      responseType: 'formData',
    });
    setLoading(false);
    const href = window.URL.createObjectURL(
      new Blob([responseData], { type: 'application/vnd.ms-excel;charset=utf-8' }),
    );
    const link = document.createElement('a');
    link.href = href;
    link.download = `${companyName + equipData.name}${exportName}报表`;
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
    window.URL.revokeObjectURL(href);
  };

  /**
   * @description Popover弹出内容content
   */
  const popoverPontent: React.JSX.Element = (
    <div
      style={{
        maxHeight: 300,
        width: 150,
        overflowY: 'auto',
      }}
    >
      {columnList.length >= 1 ? (
        <Checkbox.Group
          value={checkValue}
          // options={columnList.map((item) => ({ label: item.name, value: item.code }))}
          onChange={(e) => {
            if (e.length === 0) return;
            setCheckValue(e as string[]);
            checkValueChange(e as string[]);
          }}
        >
          <Row>
            {columnList.map((item) => {
              return (
                <Col key={item.code} span={24}>
                  <Checkbox value={item.code}>{item.name}</Checkbox>
                </Col>
              );
            })}
          </Row>
        </Checkbox.Group>
      ) : (
        <Empty />
      )}
    </div>
  );

  useEffect(() => {
    if (open) {
      const { rangeDate } = cacheData;
      setShowEnd(cacheData.sourceType === 'daily');
      setFilterCol([...fixedCol, ...dealTreeData(columnsData)]);
      setColumnList([...columnsData]);
      setCheckValue(columnsData.map((column) => column.code));
      form.setFieldsValue({
        formCompany: companyData || [],
        deviceCode: deviceData || '',
        monitorType: monitorType || null,
        dataSourceType: cacheData.sourceType || 'realTime',
        startDate: (rangeDate && rangeDate[0]) || moment(),
        endDate: (rangeDate && rangeDate[1]) || moment(),
      });
      setDeviceDataList([...deviceList]);
      if (monitorKey && monitorKey !== 'A22A08A01') {
        getMonitorList();
      }
      getDeviceData(companyData);
    }
    return () => {
      setReportType('date');
      setFilterCol([...fixedCol]);
      setResData([]);
      setCheckValue([]);
      setColumnList([]);
      setDeviceDataList([]);
      form.resetFields();
    };
  }, [open]);

  useEffect(() => {
    const newList = [];
    columnList.forEach((item) => {
      checkValue.forEach((str) => {
        if (item.code === str) {
          newList.push(item);
        }
      });
    });
    // checkValue.forEach((item) => {
    //   columnList.forEach((str) => {
    //     if (item === str.code) {
    //       newList.push(str);
    //     }
    //   });
    // });
    setFilterCol([...fixedCol, ...dealTreeData(newList)]);
  }, [JSON.stringify(checkValue)]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Modal
        title="报表"
        visible={open}
        key="exportReportModl"
        closable
        width="80%"
        onCancel={() => onResult()}
        footer={null}
        className={style['gas-export-modal']}
        destroyOnClose
      >
        <Form
          form={form}
          name="exportReportForm"
          className={style['ant-gasleak-form']}
          onFinish={() => getDataList()}
          initialValues={{ formReport: reportType }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name="formCompany" label="所属单位">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getDeviceData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getDeviceData();
                  }}
                >
                  {(companyList || []).map((item) => (
                    <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            {monitorKey !== 'A22A08A01' && (
              <Col span={6}>
                <Form.Item name="monitorType" label="监测类型">
                  <Select
                    placeholder="请选择"
                    showSearch
                    style={{ fontSize: 12 }}
                    onChange={() => {
                      form.resetFields(['deviceCode']);
                      getDeviceData();
                    }}
                  >
                    {(monitorList || []).map((item) => (
                      <Select.Option key={item.code}>{item.text}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
            <Col span={6}>
              <Form.Item name="deviceCode" label="设备名称">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getColData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getColData();
                  }}
                >
                  {(deviceDataList || []).map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="formReport" label="类型">
                <Select
                  placeholder="请选择"
                  onChange={(value) => {
                    setReportType(value);
                  }}
                >
                  {reportTypeData.map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            {reportType === 'date' && (
              <Col span={6}>
                <Form.Item name="dataSourceType" label="数据源">
                  <Select
                    placeholder="请选择"
                    onSelect={(e: SourceType['code']) => {
                      if (e === 'daily') {
                        setShowEnd(true);
                      } else {
                        setShowEnd(false);
                      }
                    }}
                  >
                    {dateList.map((item) => (
                      <Select.Option key={item.code}>{item.name}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
            <Col span={8} style={{ display: 'flex' }}>
              <Form.Item name="startDate" label="监测时间">
                <DatePicker
                  style={{ width: '100%' }}
                  picker={reportType === 'month' ? 'month' : 'date'}
                />
              </Form.Item>
              {reportType === 'date' && showEnd && (
                <Form.Item name="endDate" label="至">
                  <DatePicker style={{ width: '100%' }} picker="date" />
                </Form.Item>
              )}
            </Col>
            <Col span={8} style={{ textAlign: 'end' }}>
              <Button className={style['top-button']} type="primary" htmlType="submit">
                查询
              </Button>
              <Button
                style={{ marginLeft: 15 }}
                className={style['top-button']}
                type="primary"
                onClick={uploadTemplate}
              >
                导出Excel
              </Button>
            </Col>
          </Row>
        </Form>
        <Divider style={{ margin: 0 }} dashed />
        <div className={style['export-popover']}>
          <Popover
            placement="bottomRight"
            overlayClassName={style['online-export-popover-content']}
            trigger="click"
            title={null}
            content={popoverPontent}
          >
            <Button type="primary" size="small" ghost>
              因子配置
            </Button>
          </Popover>
        </div>
        <Table
          loading={loading}
          bordered
          rowKey={(row) => row.id}
          dataSource={resData}
          className={style['export-table']}
          rowClassName={(_, index) => {
            if (index % 2 === 0) {
              return style['even-row'];
            }
            return style['odd-row'];
          }}
          columns={filterCol}
          scroll={{ x: checkValue.length * 180, y: 500 }}
        />
      </Modal>
    </div>
  );
};
export default exportReportModl;
