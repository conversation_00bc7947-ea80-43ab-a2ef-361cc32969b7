import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { message, Input, Button, Spin } from 'antd';
import moment from 'moment';
import { alarmDispose } from '@/service/envApi';
import type { Form } from '@formily/core/esm/models';
import type { ResponseType } from '@/service/envApi';
import style from './alarm.module.less';

type PropsTypes = {
  dataObj: onLineMonitorTypes.objType;
  closeModal: () => void;
  /** handle:处置、view:查看 */
  modalType: 'handle' | 'view';
};

/**
 * @description  报警 查看、处置
 * @returns
 */
const EnvAirAlarmModal: React.FC<PropsTypes> = ({ dataObj, closeModal = () => {}, modalType }) => {
  const { TextArea } = Input;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  useEffect(() => {
    if (dataObj && dataObj.id && dataObj.id !== '') {
      form.setValues({
        ...dataObj,
        id: dataObj.id,
        disposeTm: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      });
    } else {
      message.error('数据出错').then(() => {
        closeModal();
      });
    }
  }, [dataObj]);

  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  const submitAddData: (data: Record<string, unknown>) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: ResponseType = await alarmDispose(data);
    if (res && res.code && res.code === 200) {
      message.success('处置报警成功');
      closeModal();
    } else {
      message.error('处置报警失败');
    }
    setIsLoading(false);
  };

  const save: () => void = () => {
    form.validate().then(() => {
      const submitData: Record<string, unknown> = {
        ...form.values,
      };
      submitAddData(submitData);
    });
  };

  return (
    <div className={style['alarm-view-template-container']}>
      <Spin spinning={isLoading}>
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="id"
            title="id"
            labelType={1}
            required={false}
            display="hidden"
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="supplyUnit"
            title="所属单位"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="equipNm"
            title="设备名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="equipCd"
            title="设备编号"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="description"
            title="监测对象"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="indexNm"
            title="监测指标名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="alarmLevelText"
            title="报警级别"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="disposePsn"
            title="处置人"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              disabled: modalType !== 'handle',
            }}
          />
          <YTHForm.Item
            name="disposeTm"
            title="处置时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              disabled: true,
              placeholder: '',
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
            }}
          />
          <YTHForm.Item
            name="disposeContent"
            title="处置内容"
            mergeRow={2}
            required
            component={TextArea}
            componentProps={{
              disabled: modalType !== 'handle',
            }}
          />
        </YTHForm>

        {modalType === 'handle' && (
          <div className={style['alarm-handle-filter-operation']}>
            <Button onClick={cancel} className={style['reset-btn']}>
              取消
            </Button>

            <Button onClick={save} className={style['search-btn']} type="primary">
              保存
            </Button>
          </div>
        )}
      </Spin>
    </div>
  );
};

export default EnvAirAlarmModal;
