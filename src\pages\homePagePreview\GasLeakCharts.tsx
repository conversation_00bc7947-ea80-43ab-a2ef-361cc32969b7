import React, { useEffect, useMemo, useRef } from 'react';
import * as echarts from 'echarts/core';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';

import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from 'echarts/components';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  BarChart,
  Pie<PERSON>hart,
  CanvasRenderer,
  LegendComponent,
  UniversalTransition,
]);

/**
 *
 * @description 在线率 饼图echarts
 * @returns
 */
const EChartsComponent: React.FC<{ value: number }> = (props) => {
  const { value = 0 } = props;
  const ref = useRef(null);

  const option = useMemo(() => {
    return {
      // backgroundColor: "#F7F7F7",
      // 全局颜色，顺序对应每个 series
      color: ['#68A54A', '#A9CBA2', '#E2E2E2'],
      title: {
        text: '在线率',
        top: '56%',
        left: '40%',
        textStyle: {
          color: '#333',
          fontStyle: 'normal',
          fontWeight: 'normal',
          fontFamily: 'sans-serif',
          fontSize: 12,
        },
      },
      series: [
        {
          name: '中间环形',
          type: 'pie',
          // 圆，半径，值1内圆，值2外圆
          radius: ['72%', '93%'],
          // 饼图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          hoverAnimation: false,
          // 数据标签
          label: {
            show: false,
            position: 'center',
            textStyle: {
              fontSize: 20,
              fontWeight: 'bold',
            },
            formatter: '{b}\n{c}%',
            // 针对 center 参数校正标签显示位置
            lineHeight: 3,
          },
          data: [
            {
              value,
              name: '',
              label: {
                normal: {
                  show: true,
                },
              },
            },
            {
              value: 100 - value,
              name: '',
            },
          ],
        },
        {
          name: '最内圈',
          type: 'pie',
          // 圆，半径，值1内圆，值2外圆
          radius: ['69%', '70%'],
          // 饼图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          hoverAnimation: false,
          // 数据标签
          label: {
            show: false,
          },
          itemStyle: {
            color: '#E2E2E2',
          },
          data: [
            {
              value: 100,
            },
          ],
        },
        {
          name: '最外圈',
          type: 'pie',
          // 圆，半径，值1内圆，值2外圆
          radius: ['94%', '95%'],
          // 饼图的中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          hoverAnimation: false,
          // 数据标签
          label: {
            show: false,
          },
          itemStyle: {
            color: '#E2E2E2',
          },
          data: [
            {
              value: 100,
            },
          ],
        },
      ],
    };
  }, [value]);

  useEffect(() => {
    if (ref.current) {
      // 初始化图表
      const chart = echarts.init(ref.current);
      chart.setOption(option);
    }
  }, [value]);

  return <div ref={ref} style={{ height: 100 }} />;
};
export default YTHLocalization.withLocal(EChartsComponent, locales, YTHLocalization.getLanguage());
