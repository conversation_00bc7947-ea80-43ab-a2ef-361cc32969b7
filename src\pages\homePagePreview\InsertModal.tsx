import React, { useEffect, useMemo, useState } from 'react';
import { YTHLocalization } from 'yth-ui';
import { Select, message, Input, Button, Form, Modal, Table } from 'antd';
import { CurrentUser } from '@/Constant';
import { queryMonitorEquipList } from '@/service/envApi';
import { IYTHColumnProps } from 'yth-ui/es/components/list';
import locales from '@/locales';

import style from './home.module.less';

type propsTypes = {
  online: null | 0 | 1;
  open: boolean;
  onClose: () => void;
  typeData: onLineMonitorTypes.objType[];
  /** 是否是园区账号 */
  isPark: boolean;
  selectCompany: onLineMonitorTypes.objType;
  companyList: onLineMonitorTypes.objType[];
};

type queryTableTypes = {
  name?: string;
  code?: string;
  companyId?: string;
};

/**
 * @description 接入数量、在线数量、掉线数量 查看modal
 * @returns
 */
const InsertModal: React.FC<propsTypes> = (props) => {
  const [form] = Form.useForm();
  const { open, onClose, typeData, online, isPark, selectCompany, companyList } = props;
  const [tableList, setTableListt] = useState<Record<string, string | number>[]>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取tableList数据
  const queryTableList: (value?: queryTableTypes) => Promise<void> = async (
    value?: queryTableTypes,
  ) => {
    setLoading(true);
    const data: {
      code: number;
      data: Record<string, string | number>[];
      msg: string;
    } = await queryMonitorEquipList({
      name: value?.name ?? null,
      code: value?.code ?? null,
      companyId: value?.companyId ?? null,
      online,
    });
    if (data.code === 200) {
      setTableListt(data?.data ?? []);
      setLoading(false);
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
      setLoading(false);
    }
  };

  const columnsData: IYTHColumnProps[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (v, r, index) => {
        return index + 1;
      },
    },
    {
      title: '单位名称',
      dataIndex: 'supplyUnit',
      key: 'supplyUnit',
      width: 180,
      align: 'center',
    },
    {
      title: '设备编码',
      dataIndex: 'code',
      key: 'code',
      width: 180,
      align: 'center',
    },
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      align: 'center',
    },
    {
      title: '设备类型',
      dataIndex: 'type',
      key: 'type',
      width: 180,
      align: 'center',
      render: (v) => {
        return typeData.find((item) => item.key === v)?.name || '-';
      },
    },
  ];

  const onFinish: (values: queryTableTypes) => void = (values) => {
    queryTableList(values);
  };

  // 根据online 状态 判断显示名称
  const title: string = useMemo(() => {
    if (online === null) {
      return '接入数量详情';
    }
    if (online === 1) {
      return '在线数量详情';
    }
    if (online === 0) {
      return '掉线数量详情';
    }
    return '';
  }, [online]);

  useEffect(() => {
    if (open) {
      if (isPark) {
        queryTableList({
          companyId: selectCompany?.value === 'all' ? undefined : selectCompany?.value,
        });
        form.setFieldsValue({
          companyId: selectCompany?.value === 'all' ? undefined : selectCompany?.value,
        });
      } else {
        queryTableList({ companyId: CurrentUser()?.unitCode });
        form.setFieldsValue({ companyId: CurrentUser()?.unitCode });
      }
    }
    setIsModalOpen(open);
    return () => {
      form.resetFields();
    };
  }, [open, isPark, online, selectCompany]);

  return (
    <Modal
      title={title}
      visible={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
        if (onClose) onClose();
      }}
      destroyOnClose
      width="75%"
      footer={null}
    >
      <Form
        name="basic"
        layout="inline"
        form={form}
        style={{ marginBottom: 15, fontSize: 12 }}
        onFinish={onFinish}
        className={style['form-picker']}
        onReset={() => {
          if (isPark) {
            queryTableList();
          } else {
            queryTableList({ companyId: CurrentUser()?.unitCode });
            form.setFieldsValue({ companyId: CurrentUser()?.unitCode });
          }
        }}
        autoComplete="off"
      >
        <Form.Item label="单位名称" name="companyId">
          <Select
            showSearch
            placeholder="请选择"
            optionFilterProp="label"
            style={{ width: 222 }}
            disabled={!isPark}
            options={companyList?.map((item) => {
              return {
                value: item.unitCode,
                label: item.unitName,
              };
            })}
          />
        </Form.Item>
        <Form.Item label="设备编码" name="code">
          <Input placeholder="请输入设备编码" />
        </Form.Item>
        <Form.Item label="设备名称" name="name">
          <Input placeholder="请输入设备名称" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ fontSize: 12 }}>
            查询
          </Button>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="reset" style={{ fontSize: 12 }}>
            重置
          </Button>
        </Form.Item>
      </Form>
      <Table
        loading={loading}
        dataSource={tableList}
        className={style['device-table']}
        rowKey="id"
        bordered
        rowClassName={(_, index) => {
          if (index % 2 === 0) {
            return style['even-row'];
          }
          return style['odd-row'];
        }}
        pagination={false}
        columns={columnsData}
        scroll={{ y: 500 }}
      />
    </Modal>
  );
};

export default YTHLocalization.withLocal(InsertModal, locales, YTHLocalization.getLanguage());
