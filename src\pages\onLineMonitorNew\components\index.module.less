.online-export-popover-content {
	.ant-checkbox-group {
		display: flex;
		flex-direction: column;

		.ant-checkbox-group-item {
			padding: 3px;
		}
	}
}

.ant-input,
.ant-form-item-label>label,
.ant-select-selector,
.ant-picker,
.ant-select,
.ant-select-item-option-content,
.ant-btn {
	font-size: 12px;
}

.even-row {
	&>td {
		background-color: #fff;
	}
}

.odd-row {
	&>td {
		background-color: #f8f9fa;
	}
}



.ant-gasleak-form {
	margin-bottom: 8px;
	background-color: #fff;
	padding: 10px;
	height: 120px;

	:global {
		.ant-form-item-label>label {
			font-size: 12px;
		}

		.ant-select {
			font-size: 12px;
		}

		.ant-picker-input>input {
			font-size: 12px;
		}
	}
}


.gas-table-list {
	background-color: #fff;
	overflow: auto scroll;

	.ant-spin-container {
		height: 100%;
	}

	:global {
		.ant-table-body {
			height: calc(100vh - 430px) !important;
		}

		.ant-table-tbody>tr>td {
			height: 32px;
			font-size: 12px !important;
			padding: 0 16px !important;
			border-color: rgb(244 244 244) !important;
			line-height: 32px !important;
			border-bottom-width: 1px !important;
			border-bottom-style: solid !important;
			border-right-width: 1px !important;
			border-right-style: solid !important;
		}

		.ant-table-tbody>tr:hover>td {
			background-color: #ECF6FF !important;
		}

		.ant-table-thead>tr>th {
			padding: 10px 16px;
			font-weight: bold;
			height: 40px;
			background-color: #F8F9FA;
			font-size: 12px;
		}

		.ant-table-pagination.ant-pagination {
			height: 40px;
			font-size: 12px;
		}
	}
}

.gas-export-modal {
	:global {
		.ant-modal-body {
			padding: 8px !important;
			background-color: #E5EAEE;
		}
	}

	.export-popover {
		padding: 10px;
		display: flex;
		justify-content: end;
		background-color: #fff;

	}

	.export-table {
		background-color: #fff;

		:global {
			.ant-table-body {
				background-color: #fff !important
			}



			.ant-table-thead>tr>th {
				padding: 10px 16px;
				font-size: 12px;
				font-weight: bold;
				height: 40px;
				background-color: #F8F9FA
			}

			.ant-table-tbody>tr>td {
				height: 32px;
				font-size: 12px !important;
				padding: 0 16px !important;
				border-color: rgb(244 244 244) !important;
				line-height: 32px !important;
				border-bottom-width: 1px !important;
				border-bottom-style: solid !important;
				border-right-width: 1px !important;
				border-right-style: solid !important;
			}

			.ant-table-tbody>tr:hover>td {
				background-color: #ECF6FF !important;
			}

			/* 更具体的选择器 - 必须在通用选择器之后 */
			.ant-table-tbody > tr.ant-table-placeholder:hover > td {
				background-color: #fff !important;
			}
		}
	}
}






.form-picker {
	.ant-form-item-control-input {
		border: 1px solid #d9d9d9;
		border-radius: 2px
	}
}

.modal-content-circle {
	display: flex;
	align-items: center;

	.anticon {
		font-size: 18px;
		padding-right: 10px;
		color: #FAAD14;
	}
}

.triangle {
	color: #ff0303;
}

.top-button {
	margin: 0 8px;
	padding: 0 25px;
	font-size: 12px;
}


.tag-list {
	display: flex;
	align-items: center;
	font-size: 12px;
}

.tag-key {
	margin-left: 10px;
}

.tag-key::before {
	content: '';
	display: inline-block;
	width: 1px;
	height: 12px;
	margin-bottom: -1px;
	background-color: #979797;
	margin-right: 10px;
}

.tag-list-item:first-child .tag-key::before {
	display: none;
}

.tag-list-item {
	display: flex;
	align-items: center;
}

.tag-list-item-content {
	display: flex;
	align-items: center;
	margin-left: 10px;
}
