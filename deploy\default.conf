gzip            on;  #开启Gzip压缩
gzip_min_length 1k;  #不压缩临界值，大于1K的才压缩
gzip_comp_level 6;   #压缩级别（1-9）越大越慢，同时也最消耗CPU
#声明压缩文件（MIME类型）
gzip_types      application/json text/plain application/x-javascript text/css application/xml text/javascript image/jpeg;
gzip_disable    "MSIE [1-6]\."; #禁用IE1-6

#强制取消浏览器缓存
add_header Cache-Control no-cche;
add_header Pragma no-cache;
add_header Expires 0;

client_max_body_size 100m;

server {
    listen       80;
    server_name  localhost;

    root        /usr/share/nginx/html;

    #禁用读取超时重试机制
    proxy_next_upstream off;
    #读取超时
    proxy_read_timeout 60000;
    #连接超时
    proxy_connect_timeout 2;

    location / {
        # 用于配合 browserHistory使用
        try_files $uri $uri/ /index.html;
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'Content-Type';
        proxy_set_header   Host $host:$server_port;
        proxy_set_header   REMOTE-HOST $remote_addr;
        proxy_set_header   X-Forwarded-Proto $scheme;
        proxy_set_header   X-Real-IP         $remote_addr;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}
