import { CurrentUser } from '@/Constant';

interface FormattedItem {
  id: string;
  code: string;
  name: string;
  type: string;
  children: FormattedItem[];
}

interface TreeItem {
  unitCode?: string;
  id?: string;
  unitName?: string;
  type?: string;
  children?: TreeItem[];
}

interface DataItem {
  code?: string;
  id?: string;
  [key: string]: unknown;
}

type FormattedData = Record<string, string>;

// 筛选条件
export const formatCondition = (data: Record<string, unknown>): FormattedData => {
  const info = JSON.parse(JSON.stringify(data));
  const keys = Object.keys(info);
  const obj: FormattedData = {};
  if (keys.length > 0) {
    // 使用Object.keys代替for...in循环
    Object.keys(data).forEach((k) => {
      if (typeof data[k] !== 'object') {
        obj[k] = String(data[k]);
      } else if (data[k] instanceof Array) {
        const list = (data[k] as DataItem[]).map((item: DataItem) => item?.code ?? item?.id ?? '');
        obj[k] = list.join(',');
      } else if (data[k]) {
        obj[k] = (data[k] as DataItem).code ?? '';
      }
    });
    return obj;
  }
  return {};
};

// 递归处理
export const formatTree = (list: TreeItem[]): FormattedItem[] => {
  const arr: FormattedItem[] = [];
  if (list && list.length > 0) {
    list.forEach((item) => {
      arr.push({
        id: item.unitCode || '',
        code: item.id || '',
        name: item.unitName || '',
        type: item.type === '-2' ? 'org' : 'dept',
        children: formatTree(item.children || []),
      });
    });
  }
  return arr;
};

/**
 * @description 列表行权限判断
 * @param userId 用户ID
 * @returns 是否有权限
 */
export const authRoleByTable = (userId?: string): boolean => {
  const userInfo = CurrentUser();
  if (userInfo?.unitType === '-1') {
    if (userId && userId === userInfo?.id) {
      return true;
    }
    return false;
  }
  return false;
};

/**
 * @description 只有园区层登入才能操作
 * @example unitType == '-1' 才能新增
 * @returns 是否有权限
 */
export const authRoleByNormal = (): boolean => {
  const userInfo = CurrentUser();
  return userInfo?.unitType === '-1';
};
