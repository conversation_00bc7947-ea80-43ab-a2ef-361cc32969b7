import React, { useState, useRef, Key } from 'react';
import { YTHList, YTHDialog, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message, Space } from 'antd';
import { getUuid } from '@/utils/customMethod';
import locales from '@/locales';
import { formatTree } from '@/utils/format';
import { queryUnitTreeData } from '@/service/baseModuleApi';
import type { ResponseType, ResponsPageType } from '@/service/envApi';
import formApi from '@/service/formApi';
import {
  deleteEnvQualityById,
  queryEnvQualityMonitorList,
  queryEquipInfoByCompanyId,
} from '@/service/envApi';
import MonitorDrawer from './MonitorDrawer';
import style from './baseInfo.module.less';

/** 监测设备类型字典值 */
const dictKey: string = 'A22A08A07';

type TextCodeType = { text: string; code: string }[];
type DataObjType = {
  id?: string;
  [key: string]: React.Key;
};
type filterType = {
  type?: string;
  companyId?: string;
  monitorType?: string;
  code?: string;
  state?: string;
  equipType?: string;
};

/**
 * @description 污染源监测 基础信息管理
 * @returns
 */
const PollutionSourceOnLineMonitorList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<string>(''); // 是否是新增模式
  const [dataObj, setDataObj] = useState<DataObjType>({}); // 查看或编辑行数据
  // const [isCompanyAdmin, setIsCompanyAdmin] = useState<boolean>(false); // 标记是否是企业管理员
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  // 根据所属单位和检测类型 查询设备数据
  const getAccessList: () => Promise<void> = async () => {
    const monitorItem: Record<string, string>[] = aa.getQuery().monitorType || [];
    const companyItem: Record<string, string>[] = aa.getQuery().orgCd || [];
    aa.setQuery({ monitorType: monitorItem, orgCd: companyItem, equipCd: [] });
    if (companyItem) {
      aa.getQueryForm()
        .query('*')
        .forEach(
          (item: {
            path: { entire: string };
            setComponentProps: (props: unknown) => void;
            setValue: (value: unknown) => void;
          }) => {
            if (item.path.entire === 'equipCd') {
              item.setValue([{ text: null, code: null, id: null }]);
              item.setComponentProps({
                key: getUuid(16, 17),
                request: async () => {
                  const resData: ResponseType = await queryEquipInfoByCompanyId({
                    companyId: companyItem[0]?.id,
                    monitorType: monitorItem[0]?.code,
                    type: dictKey,
                  });
                  if (resData?.data instanceof Array && resData?.data?.length > 0) {
                    const result: { text: string; code: string }[] = resData.data.map((strItem) => {
                      return { text: strItem.name, code: strItem.code };
                    });
                    return result ?? [];
                  }
                  return [];
                },
                searchable: true,
              });
            }
          },
        );
    }
  };

  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'orgCd',
      title: '所属单位',
      width: 180,
      query: true,
      display: true,
      componentName: 'PickData',
      componentProps: {
        requestData: async () => {
          try {
            return formatTree(await queryUnitTreeData());
          } catch {
            return [];
          }
        },
        onChange: getAccessList,
        multiple: false,
        p_props: {
          placeholder: '请输入',
        },
      },
      render: (_v, record) => {
        return record.supplyUnit || '-';
      },
    },
    {
      dataIndex: 'monitorType',
      title: '监测类型',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          placeholder: '请选择',
        },
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: dictKey,
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: TextCodeType = [];
          if (list instanceof Array && list.length > 0) {
            list.forEach((item) => {
              if (item.code !== 'A22A08A07A05') {
                plOp.push({
                  code: item.code,
                  text: item.text,
                });
              }
            });
          }
          return plOp;
        },
        onChange: getAccessList,
      },
      render: (_v, record) => {
        return record.monitorTypeText || '-';
      },
    },
    {
      dataIndex: 'equipCd',
      title: '设备名称',
      width: 180,
      query: true,
      display: false,
      componentName: 'Selector',
      componentProps: {
        multiple: false,
        p_props: {
          placeholder: '请选择',
        },
      },
    },

    {
      dataIndex: 'name',
      title: '设备名称',
      width: 180,
      query: false,
      display: true,
    },

    {
      dataIndex: 'equipType',
      title: '设备类型',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A05',
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: TextCodeType = [];
          if (list instanceof Array && list.length > 0) {
            list.forEach((item) => {
              plOp.push({
                code: item.code,
                text: item.text,
              });
            });
          }
          return plOp;
        },
        p_props: {
          changeOnSelect: true,
          placeholder: '请输入',
        },
      },
      render: (_v, record) => {
        return record.equipTypeText || '-';
      },
    },
    {
      dataIndex: 'code',
      title: '设备编码',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'brand',
      title: '品牌',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'stateText',
      title: '设施运行状态',
      width: 180,
      componentName: 'Selector',
      query: true,
      display: true,
      componentProps: {
        p_props: {
          allowClear: true,
        },
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A03',
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: TextCodeType = [];
          if (list instanceof Array && list.length > 0) {
            list.forEach((item) => {
              plOp.push({
                code: item.code,
                text: item.text,
              });
            });
          }
          return plOp;
        },
      },
    },
    {
      dataIndex: 'radius',
      title: '覆盖范围',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'description',
      title: '监测对象',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'location',
      title: '设备位置',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'height',
      title: '高度',
      width: 180,
      query: false,
      display: true,
    },
  ];

  const setModalTitle: () => string = () => {
    if (modalType === 'add') {
      return '新增';
    }
    if (modalType === 'view') {
      return '查看';
    }
    if (modalType === 'edit') {
      return '编辑';
    }
    return '';
  };

  // 关闭弹窗
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    aa.reload({});
    Modal.destroyAll();
  };

  const confirmDelete: (row: { id: string }) => Promise<void> = async (row) => {
    const res: ResponseType = await deleteEnvQualityById(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
      aa.reload({});
    } else {
      message.error('删除数据出错');
    }
  };

  const deleteTemplateDialog: (row: { id: string }) => void = (row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  const handleFilter: (f: Record<string, Record<string, string>[]>) => filterType = (f) => {
    const filter: filterType = {};
    filter.type = dictKey;
    if (f.orgCd && f.orgCd.length > 0) {
      filter.companyId = f.orgCd[0].id;
    }
    if (f.monitorType && f.monitorType.length > 0) {
      filter.monitorType = f.monitorType[0].code;
    }
    if (f.equipCd && f.equipCd.length > 0) {
      filter.code = f.equipCd[0].code;
    }
    if (f.stateText && f.stateText.length > 0) {
      filter.state = f.stateText[0].code;
    }
    if (f.equipType && f.equipType.length > 0) {
      filter.equipType = f.equipType[0].code;
    }
    return filter;
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="pollutionSourceOnLineMonitorList"
        action={aa}
        actionRef={ref}
        showRowSelection={false}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setEditMenuVisiable(true);
                    setModalType('add');
                  }}
                >
                  新增
                </Button>
              </div>
            ),
          },
        ]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ResponsPageType = await queryEnvQualityMonitorList({
            descs: [''],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            /*
             */
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          message.error('请求数据出错，请刷新重试或联系管理员');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={200}
        rowOperation={(row: { id: string; [key: string]: Key }) => {
          return [
            {
              element: (
                <div className={style['gas-leak-monitor-row-operator']}>
                  <Space size="middle">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('view');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      查看
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('edit');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      编辑
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        deleteTemplateDialog(row);
                      }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="60%"
        title={setModalTitle()}
        footer={null}
        visible={editMenuVisiable}
        onCancel={closeModal}
        destroyOnClose
        maskClosable={false}
      >
        <MonitorDrawer
          type={modalType}
          closeModal={closeModal}
          dataObj={dataObj}
          dictKey={dictKey}
        />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  PollutionSourceOnLineMonitorList,
  locales,
  YTHLocalization.getLanguage(),
);
