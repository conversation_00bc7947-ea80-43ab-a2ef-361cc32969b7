apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: CIP
    component: campus-front-environment
    tier: backend
  name: campus-front-environment
  namespace: campus
spec:
  progressDeadlineSeconds: 600
  replicas: 1 #节点数
  selector:
    matchLabels:
      app: CIP
      component: campus-front-environment
      tier: backend
  template:
    metadata:
      labels:
        app: CIP
        component: campus-front-environment
        tier: backend
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
      containers:
        - name: campus-front-environment
          env:
            - name: active
              value: dev
          image: $REGISTRY/$HARBOR_NAMESPACE/$active/campus-front-environment:v$GITVERSION-$BUILD_TIMESTAMP
          imagePullPolicy: Always
          ports:
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: 2000m
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 512Mi
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      imagePullSecrets:
        - name: harbor
      dnsPolicy: <PERSON><PERSON><PERSON><PERSON><PERSON>
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
