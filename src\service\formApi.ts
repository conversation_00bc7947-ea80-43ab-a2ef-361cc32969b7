import { formRequest } from '../request';

// 获取字典子节点数据
const getDict = async (parentCode: string) => {
  const resp = await formRequest.get(`/getDic/${parentCode}`);

  const { data = [] } = resp ?? {};

  return data;
};
// 化工园区项目获取字典子节点数据
const getDictionary = async (params = {}) => {
  const resp = await formRequest.post('/dataDictionary/query', {
    data: params,
  });

  const { data = {} } = resp ?? {};

  return data;
};

// 获取字典树形数据
const getTreeDict = async (parentCode: string) => {
  const resp = await formRequest.get(`/getTreeDic/${parentCode}`);

  const { data = [] } = resp ?? {};

  return data;
};

/** 获取字典子节点数据 */
const getDictData = async (code?: string) =>
  !code ? [] : ((await formRequest.get(`/V2/api/getDic/${code}`))?.data ?? []);

export default { getDict, getTreeDict, getDictionary, getDictData };
