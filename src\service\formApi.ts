import { formRequest } from '../request';

type DictParams = {
  condition: Record<string, string | number>;
  currentPage: number;
  pageSize: number;
};

type ResponseType = {
  data: Record<string, string | number>[];
  code: number;
  msg: string;
};

// 获取字典子节点数据
const getDict: (parentCode: string) => Promise<Record<string, string | number>[]> = async (
  parentCode: string,
) => {
  const resp: ResponseType = await formRequest.get(`/getDic/${parentCode}`);

  const { data = [] } = resp ?? {};

  return data;
};
// 化工园区项目获取字典子节点数据
const getDictionary: (params: DictParams) => Promise<{ list: Record<string, string>[] }> = async (
  params,
) => {
  const resp: { code: number; data: { list: Record<string, string>[] }; msg: string } =
    await formRequest.post('/dataDictionary/query', {
      data: params,
    });

  const { data = { list: [] } } = resp ?? {};

  return data;
};

// 获取字典树形数据
const getTreeDict: (parentCode: string) => Promise<Record<string, string | number>[]> = async (
  parentCode: string,
) => {
  const resp: ResponseType = await formRequest.get(`/getTreeDic/${parentCode}`);

  const { data = [] } = resp ?? {};

  return data;
};

/** 获取字典子节点数据 */
const getDictData: (code?: string) => Promise<Record<string, string | number>[]> = async (
  code?: string,
) => (!code ? [] : ((await formRequest.get(`/V2/api/getDic/${code}`))?.data ?? []));

export default { getDict, getTreeDict, getDictionary, getDictData };
