import React, { useState, useEffect, useRef } from 'react';
import {
  DatePicker,
  Form,
  Button,
  Table,
  Row,
  Col,
  message,
  Popover,
  Checkbox,
  Select,
  Modal,
  Empty,
} from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';
import { YTHLocalization } from 'yth-ui';
import moment from 'moment';
import locales from '@/locales';
import { CurrentUser } from '@/Constant';
import formApi from '@/service/formApi';
import {
  queryUnitInfoByType,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  queryMonitorIndexValueItem,
} from '@/service/envApi';

import type { IYTHColumnProps } from 'yth-ui/es/components/list/index';
import { monitorTypeConfigs, dateList, dataformat, tagList } from './constants';
import { OnlineMonitorProps, ObjectType, DateListTypes } from './types';
import style from './index.module.less';
import ExportReport from './ExportReport';
import getTableScroll from './GetTableScroll';
import GasLeakCharts from './GasLeakCharts';

const { RangePicker } = DatePicker;

// 监测时间列定义，固定在表格左侧
const FIXED_COLUMNS: IYTHColumnProps[] = [
  {
    code: 'collectTime',
    name: '监测时间',
    width: 180,
    title: '监测时间',
    align: 'center',
    dataIndex: 'collectTime',
    sorter: true,
    render: (_r, record) => {
      return record.TIME || '-';
    },
    fixed: 'left',
    key: 'collectTime',
  },
];

/**
 * @description 时间范围类型
 * 用于处理时间范围的计算
 */
type ChangeRangeType = (
  rangeDate: moment.Moment[] | moment.Moment, // 时间范围
  type: string, // 时间类型
  startTime?: boolean, // 是否是开始时间
) => string;

/**
 * @description 通用在线监测组件
 * 该组件支持两种不同类型的在线监测：
 * 1. 环境质量监测 (envQuality)
 * 2. 污染源监测 (wasteGas)
 *
 * 组件功能包括：
 * - 表单查询（所属单位、监测类型、设备名称、时间类型、监测时间）
 * - 表格展示监测数据
 * - 因子配置（表头列选择）
 * - 报表导出
 * - 统计图表展示
 *
 * @param {OnlineMonitorProps} props - 组件属性
 * @returns {React.ReactElement} 在线监测页面组件
 */
const OnlineMonitor: React.FC<OnlineMonitorProps> = ({ monitorConfigType, customConfig = {} }) => {
  // 获取对应监测类型的配置
  const config = { ...monitorTypeConfigs[monitorConfigType], ...customConfig };
  const { monitorKey, onLineMonitorType } = config;

  // 表单实例
  const [form] = Form.useForm();
  // 表格引用，用于计算表格高度
  const tableRef = useRef<HTMLDivElement>(null);

  // 状态管理
  const [columnList, setColumnList] = useState<IYTHColumnProps[]>([]); // 原始column表头数据
  const [filterCol, setFilterCol] = useState<IYTHColumnProps[]>([]); // 根据因子配置筛选的column数据
  const [listDatas, setListData] = useState<ObjectType[]>([]); // 接口返回数据
  const [checkValue, setCheckValue] = useState<React.Key[]>([]); // 因子配置选择的可见表头项
  const [loading, setLoading] = useState<boolean>(false); // 加载状态
  const [deviceList, setDeviceList] = useState<ObjectType[]>([]); // 根据选择的公司查询出的设备数据
  const [modalViseble, setModalViseble] = useState<boolean>(false); // 导出modal显示状态
  const [scrollY, setScrollY] = useState<number | string>(0); // 表格Y轴滚动高度
  const [chartType, setChartType] = useState<'avg' | 'sum'>('avg'); // 统计图表类型，默认为均值
  const [chartsOpen, setChartsOpen] = useState(false); // 统计图表显示状态
  const [monitorList, setMonitorList] = useState<ObjectType[]>([]); // 监测类型数据
  const [companyList, setCompanyList] = useState<ObjectType[]>([]); // 所属单位数据
  const [overWeek, setOverWeek] = useState<number>(0); // 时间范围限制
  const [monitorFrequencyList, setMonitorFrequencyList] = useState<DateListTypes[]>([]); // 采集频率新数据
  const [dateTypeKey, setDateTypeKey] = useState<string>(''); // 时间类型也就是采集频率
  const [selectedEquipId, setSelectedEquipId] = useState<string>(''); // 当前选中的设备ID

  /**
   * @description 获取采集频率字典数据
   * 根据remark字段匹配对应的数据类型配置
   * @returns {Promise<void>} 处理后的监测频率数据，包含与dateList匹配的信息
   */
  const getMonitorFrequencyList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: 'A23A04',
      },
      currentPage: 0,
      pageSize: 0,
    });

    // 处理字典数据，根据remark字段匹配数据类型
    const processedList: DateListTypes[] = list?.map((item) => {
      // 使用remark字段直接匹配dateList中的dataCode
      const matchedDateConfig = dateList.find((dateItem) => dateItem.dataCode === item.remark);

      return {
        ...matchedDateConfig, // 使用匹配的配置
        key: item.remark, // 使用remark作为key
        text: item.text, // 保留原始的text作为显示文本
        code: item.code, // 保留原始的code
        id: item.id, // 保留原始的id
      };
    });
    // 确保有数据
    if (processedList && processedList.length > 0) {
      setMonitorFrequencyList(processedList);
      setDateTypeKey(processedList[0].key);
      form.setFieldsValue({
        formDateType: processedList[0].key,
        rangeDate: processedList[0].defaultRange(),
      });
    } else {
      // 如果没有数据，使用默认配置
      const defaultConfig = dateList[0]; // 使用第一个配置作为默认
      setMonitorFrequencyList([defaultConfig]);
      setDateTypeKey(defaultConfig.key);
      form.setFieldsValue({
        formDateType: defaultConfig.key,
        rangeDate: defaultConfig.defaultRange(),
      });
    }
  };

  /**
   * @description 获取监测类型数据
   * 根据不同监测类型的配置获取并过滤监测类型列表
   */
  const getMonitorList = async () => {
    // 获取监测类型字典数据
    const { list } = await formApi.getDictionary({
      condition: {
        // 根据配置决定使用哪个父级字典码
        fatherCode: config.filterMonitorList ? 'A22A08A07' : monitorKey,
      },
      currentPage: 0,
      pageSize: 0,
    });

    let finalList = list;
    // 如果需要过滤监测类型列表
    if (config.filterMonitorList && config.filterCondition) {
      finalList = list?.filter(config.filterCondition) || [];
    }

    setMonitorList([...finalList]);
  };

  /**
   * @description 修改时间范围
   * @param {moment.Moment} rangeDate - 选中的时间范围
   * @param {string} key - 采集频率字典数据中的key值
   */
  const changeRange: ChangeRangeType = (rangeDate, key, startTime = true) => {
    const dataCodeKey = monitorFrequencyList.find((item) => item.key === key)?.dataCode;
    // 年均数据 (dataCode: '9999')
    if (dataCodeKey === '9999') {
      const startDate = moment(rangeDate as moment.Moment)
        .startOf('year')
        .format('YYYY-MM-DD HH:mm:ss');
      const endDate = moment(rangeDate as moment.Moment)
        .endOf('year')
        .format('YYYY-MM-DD HH:mm:ss');
      return startTime ? startDate : endDate;
    }
    // 日均数据 (dataCode: '2031')
    if (dataCodeKey === '2031') {
      const startDate = rangeDate[0].startOf('day').format('YYYY-MM-DD HH:mm:ss');
      const endDate = rangeDate[1].endOf('day').format('YYYY-MM-DD HH:mm:ss');
      return startTime ? startDate : endDate;
    }
    // 实时数据 (dataCode: '2011') 和时均数据 (dataCode: '2061')
    return startTime
      ? rangeDate[0].format('YYYY-MM-DD HH:mm:ss')
      : rangeDate[1].format('YYYY-MM-DD HH:mm:ss');
  };

  /**
   * @description 获取监测数据列表
   * @param {string} id - 可选的设备ID
   */
  const queryListData = async (id?: string) => {
    const { rangeDate, formCompany, deviceCode, formDateType } = form.getFieldsValue();
    // 检查时间范围是否超限
    if (overWeek) {
      message.info(`监测时间不能超过${overWeek}天!!!`);
      return;
    }
    // 获取设备ID
    const equipId = id ?? deviceList.find((str) => str.code === deviceCode)?.id;

    // 构建查询参数
    const data = {
      monitorType: onLineMonitorType,
      equipCd: deviceCode,
      equipId,
      orgCd: formCompany || '',
      startTm: changeRange(rangeDate, formDateType, true),
      endTm: changeRange(rangeDate, formDateType, false),
      dateType: formDateType,
      reportType: '1',
    };
    // 开始加载
    setLoading(true);
    // 调用API获取监测数据
    const resData = await queryMonitorIndexValueItem(data);
    if (resData.code && resData.code === 200 && resData.data) {
      setListData(Array.isArray(resData.data) ? [...resData.data] : []);
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
      setListData([]);
    }
    // 结束加载
    setLoading(false);
  };

  /**
   * @description 获取表头列数据并更改结构
   * 使用当前选中的设备ID和监测频率获取监测指标
   */
  const getColData = async () => {
    // 检查必要参数：设备ID和监测频率都必须存在
    if (!selectedEquipId || !dateTypeKey) {
      // 缺少必要参数时，只显示时间列
      setFilterCol([...FIXED_COLUMNS]);
      return;
    }

    // 开始加载
    setLoading(true);
    // 清空现有数据
    setColumnList([]);
    setCheckValue([]);
    // 调用API获取监测指标
    const data = await queryMonitorIndex(selectedEquipId, dateTypeKey);
    if (data.code === 200 && data.data.length > 0) {
      const newList = (data?.data.map((item) => item.code) || []) as string[];
      const newDatas = data.data as IYTHColumnProps[];
      setColumnList([...newDatas]);
      setFilterCol([...FIXED_COLUMNS, ...newDatas]);
      setCheckValue([...newList]);
      // 获取监测数据
      queryListData(selectedEquipId);
    } else {
      // 如果没有监测指标，只显示时间列
      setFilterCol([...FIXED_COLUMNS]);
    }
    // 结束加载
    setLoading(false);
  };

  /**
   * @description 根据选择的公司获取设备列表
   */
  const getDeviceData = async () => {
    const { monitorType: formMonitorType, formCompany } = form.getFieldsValue();
    const unitCode = formCompany || CurrentUser().unitCode || '';
    // 清空现有数据
    setColumnList([]);
    setCheckValue([]);
    setListData([]);
    setFilterCol([...FIXED_COLUMNS]);
    // 开始加载
    setLoading(true);
    // 调用API获取设备列表
    const data = await queryEquipInfoByCompanyId({
      companyId: unitCode,
      type: monitorKey,
      monitorType: formMonitorType,
      monitorOnline: '1',
    });
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      // 设置设备列表
      setDeviceList([...data.data]);
      // 默认选择第一个设备
      form.setFieldsValue({ deviceCode: data.data[0]?.code });
      // 设置选中的设备ID（这会触发useEffect自动调用getColData）
      setSelectedEquipId(data.data[0]?.id || '');
    } else {
      // 如果没有设备，清空设备选择
      form.resetFields(['deviceCode']);
      setDeviceList([]);
      setSelectedEquipId('');
    }
    // 结束加载
    setLoading(false);
  };

  /**
   * @description 获取所属单位列表
   */
  const getCompanyList = async () => {
    // 调用API获取单位列表
    const data = await queryUnitInfoByType(monitorKey);
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      // 设置单位列表
      setCompanyList([...data.data]);
      // 默认选择第一个单位
      form.setFieldsValue({ formCompany: data?.data[0]?.companyId || null });
      // 获取设备列表
      getDeviceData();
    }
  };

  /**
   * @description 将原始数据转换为表格列配置
   * 处理均值和累计值的显示逻辑
   * @param {any[]} treeData - 原始监测指标数据
   * @returns {IYTHColumnProps[]} 处理后的表格列配置
   */
  const dealTreeData = (treeData) => {
    const data = treeData.map((item, index) => {
      // 创建主列
      const newItem = {
        ...item,
        key: item.code + index + new Date().getTime(),
        dataIndex: item.code + index,
        title: item.name,
        width: 180,
        align: 'center',
      };

      // 创建均值和累计值子列
      newItem.children = dataformat.map((str) => {
        return {
          key: item.code + str.key + index,
          dataIndex: item.code + str.key + index,
          align: 'center',
          width: 90,
          title: str.value,
          children: [
            {
              key: item.code + str.key,
              dataIndex: item.code + str.key,
              align: 'center',
              title: item.measureUnit,
              // 单元格渲染逻辑
              render: (_r, record) => {
                const newVal = item.code + str.key;
                if (record[newVal]) {
                  // 查找匹配的标签配置
                  const tagConfig = tagList.find((tag) => record[newVal].includes(tag.key));

                  // 如果找到匹配的标签配置，应用对应的颜色样式
                  if (tagConfig) {
                    // 将十六进制颜色转换为带透明度的rgba格式
                    const hexToRgba = (hex: string, alpha: number = 0.5) => {
                      if (hex === '#FFFFFF') return ''; // 正常状态不设置背景色

                      const r = parseInt(hex.slice(1, 3), 16);
                      const g = parseInt(hex.slice(3, 5), 16);
                      const b = parseInt(hex.slice(5, 7), 16);
                      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
                    };

                    const cellStyle = {
                      backgroundColor: hexToRgba(tagConfig.color, 0.5), // 50% 透明度
                      padding: '2px 8px',
                    };
                    return <span style={cellStyle}>{record[newVal]}</span>;
                  }

                  // 没有找到匹配的标签配置时，正常显示
                  return record[newVal];
                }
                // 处理0值
                if (record[newVal] === 0) {
                  return '0';
                }
                return '-';
              },
            },
          ],
        };
      });
      // 如果不支持累计值，则删除累计值列
      if (!item.isTotalizeValue || item.isTotalizeValue === '0') {
        delete newItem.children[1];
        newItem.children[0].width = 180;
      }
      return newItem;
    });
    return data;
  };

  /**
   * @description 检查时间范围是否超限
   * @param {[moment.Moment, moment.Moment]} dateRange - 时间范围
   */
  const checkDateRangeLimit = (dateRange) => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) return;

    const dateConfig = monitorFrequencyList.find((item) => item.key === dateTypeKey);
    const overDay = dateConfig?.over || 7;
    if (dateRange[1].diff(dateRange[0], 'days') > overDay) {
      message.warning(`监测时间范围请勿超过${overDay}天`);
      setOverWeek(overDay);
    } else {
      setOverWeek(0);
    }
  };

  /**
   * @description 渲染不同类型的日期选择器
   * @returns {React.ReactNode} 日期选择器组件
   */
  const renderDatePicker = () => {
    const dateConfig = monitorFrequencyList.find((item) => item.key === dateTypeKey);
    // 如果没有找到配置，使用默认的日期时间选择器
    if (!dateConfig) {
      return (
        <RangePicker
          showTime
          size="large"
          format="YYYY-MM-DD HH:mm:ss"
          placeholder={['开始时间', '结束时间']}
        />
      );
    }
    // 获取表单中的日期范围值
    const dateRange = form.getFieldValue('rangeDate');
    switch (dateConfig.pickerType) {
      case 'year':
        // 年份选择器
        return (
          <DatePicker
            picker="year"
            size="large"
            format={dateConfig.format}
            value={dateRange}
            onChange={(date) => {
              if (date) {
                form.setFieldsValue({ rangeDate: date });
              }
            }}
            disabledDate={(current) => {
              // 禁用未来年份
              return current && current > moment().endOf('year');
            }}
          />
        );
      case 'date':
        // 日期选择器（不带时间）
        return (
          <RangePicker
            picker="date"
            size="large"
            format={dateConfig.format}
            value={dateRange}
            onChange={(dates) => {
              form.setFieldsValue({ rangeDate: dates });
              if (dates) {
                checkDateRangeLimit(dates);
              }
            }}
          />
        );
      case 'dateTime':
      default:
        // 日期时间选择器
        return (
          <RangePicker
            showTime
            size="large"
            format={dateConfig.format}
            value={dateRange}
            onOk={(dates) => {
              if (dates) {
                checkDateRangeLimit(dates);
              }
            }}
          />
        );
    }
  };

  // 监听因子配置变化，更新表格列
  useEffect(() => {
    if (checkValue.length === 0 || columnList.length === 0) return;
    // 创建Set去重
    const uniqueCodes = new Set(checkValue);
    // 根据选中的因子筛选列
    const newList = [];
    // 使用Set.has方法检查code是否存在
    columnList.forEach((item) => {
      if (uniqueCodes.has(item.code)) {
        newList.push(item);
      }
    });
    // 设置表格列
    setFilterCol([...FIXED_COLUMNS, ...dealTreeData(newList)]);
  }, [JSON.stringify(checkValue), columnList]);

  // 监听设备ID和采集频率变化，重新获取列数据
  useEffect(() => {
    if (selectedEquipId && dateTypeKey) {
      getColData();
    }
  }, [selectedEquipId, dateTypeKey]);

  // 组件初始化
  useEffect(() => {
    getMonitorFrequencyList();
    // 获取监测类型数据
    getMonitorList();
    // 获取所属单位数据
    getCompanyList();
    // 计算表格滚动高度
    setScrollY(getTableScroll({ extraHeight: 144, ref: tableRef }));
  }, []);

  // 表格分页配置
  const paginationProps: TablePaginationConfig = {
    showSizeChanger: true,
    total: listDatas.length ?? 0,
    defaultPageSize: 20,
    showTotal: (total: number) => {
      return `共${total}条`;
    },
  };

  /**
   * @description 因子配置弹出内容
   * 用于选择显示哪些监测指标
   */
  const popoverPontent: React.JSX.Element = (
    <div
      style={{
        width: 150,
        overflowY: 'auto',
      }}
    >
      {columnList.length === 0 ? (
        <Empty />
      ) : (
        <Checkbox.Group
          value={checkValue}
          onChange={(e) => {
            // 至少选择一项
            if (e.length === 0) return;
            setCheckValue(e as string[]);
          }}
        >
          <Row>
            {columnList.map((item) => {
              return (
                <Col key={item.code} span={24}>
                  <Checkbox value={item.code}>{item.name}</Checkbox>
                </Col>
              );
            })}
          </Row>
        </Checkbox.Group>
      )}
    </div>
  );

  return (
    <div
      style={{
        width: 'calc(100% - 16px)',
        height: '100%',
        backgroundColor: '#fff',
        margin: '8px 0',
      }}
    >
      <div>
        {/* 查询表单 */}
        <Form
          form={form}
          name="OnlineMonitorForm"
          className={style['ant-gasleak-form']}
          onFinish={() => {
            queryListData();
          }}
        >
          {/* 第一行表单项：所属单位、监测类型、设备名称、时间类型 */}
          <Row gutter={16} style={{ height: 50 }}>
            {/* 所属单位 */}
            <Col span={6}>
              <Form.Item className={style['form-picker']} name="formCompany" label="所属单位">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getDeviceData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getDeviceData();
                  }}
                >
                  {(companyList || []).map((item) => (
                    <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            {/* 监测类型 */}
            <Col span={6}>
              <Form.Item name="monitorType" label="监测类型">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  allowClear
                  onChange={() => {
                    getDeviceData();
                    form.resetFields(['deviceCode']);
                  }}
                >
                  {(monitorList || []).map((item) => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            {/* 设备名称 */}
            <Col span={6}>
              <Form.Item name="deviceCode" label="设备名称">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={(deviceCode) => {
                    // 更新选中的设备ID
                    const equipId = deviceList.find((item) => item.code === deviceCode)?.id || '';
                    setSelectedEquipId(equipId);
                  }}
                >
                  {(deviceList || []).map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            {/* 时间类型 */}
            <Col span={6}>
              <Form.Item name="formDateType" label="采集频率">
                <Select
                  placeholder="请选择"
                  style={{ fontSize: 12 }}
                  onChange={(e) => {
                    // 重置时间范围限制
                    setOverWeek(0);
                    // 设置时间类型
                    setDateTypeKey(e);
                    // 获取对应时间类型的配置
                    const dateConfig = monitorFrequencyList.find((item) => item.key === e);
                    if (dateConfig) {
                      // 设置默认时间范围
                      form.setFieldsValue({
                        rangeDate: dateConfig.defaultRange ? dateConfig.defaultRange() : [],
                      });
                    }
                  }}
                >
                  {monitorFrequencyList.map((item) => (
                    <Select.Option key={item.key}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          {/* 第二行表单项：监测时间、查询和重置按钮 */}
          <Row gutter={16} style={{ height: 50 }}>
            {/* 监测时间 */}
            <Col span={8} style={{ display: 'flex' }}>
              <Form.Item name="rangeDate" label="监测时间">
                {renderDatePicker()}
              </Form.Item>
            </Col>
            {/* 查询和重置按钮 */}
            <Col span={16} style={{ textAlign: 'end' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                {/* 查询按钮 */}
                <Button
                  style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                  type="primary"
                  htmlType="submit"
                >
                  查询
                </Button>
                {/* 重置按钮 */}
                <Button
                  style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                  onClick={() => {
                    // 重置表单
                    form.resetFields();
                    // 设置默认值
                    const defaultDateConfig = monitorFrequencyList[0] || dateList[0];
                    form.setFieldsValue({
                      formDateType: defaultDateConfig?.key || '',
                      monitorType: null,
                      formCompany: companyList[0]?.companyId,
                      rangeDate: defaultDateConfig?.defaultRange
                        ? defaultDateConfig.defaultRange()
                        : [moment().startOf('day'), moment()],
                    });
                    // 重置时间类型
                    setDateTypeKey(defaultDateConfig?.key || '');
                    // 清空设备列表和选中的设备ID
                    setDeviceList([]);
                    setSelectedEquipId('');
                    // 清空因子配置
                    setCheckValue([]);
                    // 重新获取设备列表（这会自动设置新的设备ID并触发数据查询）
                    getDeviceData();
                  }}
                >
                  重置
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
        <div style={{ height: 8, backgroundColor: '#F0F2F5' }} />
        {/* 功能按钮区域 */}
        <div
          style={{
            padding: '10px 0',
            display: 'flex',
            justifyContent: 'space-between',
            backgroundColor: '#fff',
            height: 40,
          }}
        >
          {/* tag标签 代表指标状态 */}
          <div className={style['tag-list']}>
            {tagList.map((item) => (
              <div key={item.key} className={style['tag-list-item-content']}>
                <span
                  style={{
                    width: 10,
                    height: 10,
                    backgroundColor: item.color,
                    borderColor: item.borderColor,
                    borderWidth: item.borderColor ? 1 : 0,
                    borderStyle: 'solid',
                    borderRadius: 2,
                    marginLeft: 4,
                    marginRight: 2,
                  }}
                />
                <div>
                  {item.text}({item.key})
                </div>
              </div>
            ))}
          </div>
          <div>
            {/* 报表按钮 */}
            <Button
              type="primary"
              size="small"
              style={{ marginLeft: 15, marginRight: 15 }}
              onClick={() => setModalViseble(true)}
            >
              报表
            </Button>
            {/* 统计图按钮 */}
            <Button
              type="primary"
              size="small"
              onClick={() => setChartsOpen(true)}
              style={{ marginRight: 10 }}
              ghost
            >
              统计图
            </Button>
            {/* 因子配置按钮 */}
            <Popover placement="bottomRight" trigger="click" title={null} content={popoverPontent}>
              <Button type="primary" size="small" style={{ marginRight: 10 }} ghost>
                因子配置
              </Button>
            </Popover>
          </div>
        </div>
        {/* 数据表格 */}
        <Table
          loading={loading}
          bordered
          ref={tableRef}
          className={style['gas-table-list']}
          rowKey={(row) => row.id}
          dataSource={listDatas}
          rowClassName={(_, index) => {
            // 设置行背景色
            if (index % 2 === 0) {
              return style['even-row'];
            }
            return style['odd-row'];
          }}
          style={{ width: '100%', overflow: 'auto' }}
          columns={filterCol}
          pagination={paginationProps}
          scroll={{ x: checkValue.length * 180, y: scrollY }}
        />

        {/* 报表导出弹窗 */}
        <Modal
          width="80%"
          title="报表"
          visible={modalViseble}
          onOk={() => {
            setModalViseble(false);
          }}
          onCancel={() => {
            setModalViseble(false);
          }}
          destroyOnClose
          footer={null}
        >
          <ExportReport
            monitorKey={monitorKey}
            onLineMonitorType={onLineMonitorType}
            monitorConfigType={monitorConfigType}
            companyList={companyList}
            queryData={{ ...form.getFieldsValue(), selectedEquipId }}
            monitorFrequencyList={monitorFrequencyList}
          />
        </Modal>

        {/* 统计图弹窗 */}
        <Modal
          width="80%"
          title={
            <div>
              <span>统计图</span>
              {/* 均值/累计值切换 */}
              <Select
                defaultValue="均值"
                style={{ width: 100, marginLeft: 20 }}
                onChange={(e: 'avg' | 'sum') => setChartType(e)}
                options={[
                  {
                    value: 'avg',
                    label: '均值',
                  },
                  {
                    value: 'sum',
                    label: '累计值',
                  },
                ]}
              />
            </div>
          }
          visible={chartsOpen}
          onOk={() => {
            setChartsOpen(false);
          }}
          onCancel={() => {
            setChartsOpen(false);
          }}
          destroyOnClose
          footer={null}
        >
          {/* 统计图表组件 */}
          <GasLeakCharts
            monitorKey={monitorKey}
            chartType={chartType}
            onLineMonitorType={onLineMonitorType}
            monitorConfigType={monitorConfigType}
            companyList={companyList}
            queryData={{ ...form.getFieldsValue(), selectedEquipId }}
            monitorFrequencyList={monitorFrequencyList}
          />
        </Modal>
      </div>
    </div>
  );
};

export default YTHLocalization.withLocal(OnlineMonitor, locales, YTHLocalization.getLanguage());
