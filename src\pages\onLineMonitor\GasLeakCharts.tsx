import React, { useEffect, useMemo, useRef, useState } from 'react';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from 'echarts/components';
import { Line<PERSON>hart, BarChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { YTHLocalization } from 'yth-ui';
import { Form, Button, Row, Col, DatePicker, Select, message, Spin } from 'antd';
import moment from 'moment';
import { isNumber } from 'yth-ui/es/components/util/util';
import formApi from '@/service/formApi';
import locales from '@/locales';
import {
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  queryMonitorIndexValueItem,
} from '@/service/envApi';
import style from './index.module.less';

const { RangePicker } = DatePicker;

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  BarChart,
  CanvasRenderer,
  LegendComponent,
  UniversalTransition,
]);

/**
 * 时间类型types
 */
type DateListTypes = {
  name: '实时数据' | '时均值' | '日均值';
  code: 'realTime' | 'hour' | 'daily';
  key: '1' | '2' | '3';
  format: string;
  over: 7 | 30;
};

type objType = Record<string, string>;

type LegendSelectChangedEvent = {
  name: string;
  type: 'legendselectchanged';
  selected: Record<string, boolean>;
};

type PropsType = {
  chartType: 'avg' | 'sum';
  monitorKey: string;
  envType: string;
  queryData: Record<string, string>;
  companyList: Record<string, string>[];
};

/** 时间类型 */
const dateList: DateListTypes[] = [
  { name: '实时数据', code: 'realTime', key: '1', format: 'YYYY-MM-DD HH:mm:ss', over: 7 },
  { name: '时均值', code: 'hour', key: '2', format: 'YYYY-MM-DD HH', over: 7 },
  { name: '日均值', code: 'daily', key: '3', format: 'YYYY-MM-DD', over: 30 },
];

/**
 * @description echarts 显示 折线图或柱状图
 * @param props
 * @returns
 */
const EChartsComponent: React.FC<PropsType> = (props) => {
  const { chartType, monitorKey, envType, queryData, companyList } = props;
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  const [magictype, setMagictype] = useState<'bar' | 'line'>('line');
  const [form] = Form.useForm();
  const [selected, setSelected] = useState<Record<string, boolean>>({});
  const [columnList, setColumnList] = useState<Array<Record<string, React.Key>>>([]); // 表头数据
  const [loading, setLoading] = useState<boolean>(false);
  const [deviceList, setDeviceList] = useState<objType[]>([]); // 设备数据
  const [monitorList, setMonitorList] = useState<objType[]>([]); // 监测类别
  const [listDatas, setListData] = useState<objType[]>([]); // 查询table 接口返回数据
  const [dateType, setDateType] = useState<DateListTypes['key']>('1');
  const [onlyOne, setOnlyOne] = useState<null | number>(null); // 判断是否图例只剩下一个
  const [overWeek, setOverWeek] = useState<number>(0); // 时间范围限制

  // 获取监测类型数据
  const getMonitorList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: monitorKey === 'A22A08A03' ? 'A22A08A07' : monitorKey, // 监测类型
      },
      currentPage: 0,
      pageSize: 0,
    });
    if (monitorKey === 'A22A08A03') {
      const newList = list?.filter((item) => item.code === 'A22A08A07A05') || [];
      setMonitorList([...newList]);
    } else if (monitorKey === 'A22A08A07') {
      const newList = list?.filter((item) => item.code !== 'A22A08A07A05') || [];
      setMonitorList([...newList]);
    } else {
      setMonitorList([...list]);
    }
  };

  /**
   * @description 获取tableList数据
   */
  const queryListData = async (id?: string) => {
    const { rangeDate, formCompany, deviceCode, formDateType } = form.getFieldsValue();
    const equipId = id ?? deviceList.find((str) => str.code === deviceCode)?.id;
    if (overWeek) {
      message.info(`监测时间不能超过${overWeek}天!!!`);
      return;
    }
    setLoading(true);
    const resData = await queryMonitorIndexValueItem({
      monitorType: envType,
      equipCd: deviceCode,
      equipId,
      orgCd: formCompany || '',
      startTm: rangeDate[0].format('YYYY-MM-DD HH:mm:ss'),
      endTm: rangeDate[1].format('YYYY-MM-DD HH:mm:ss'),
      dateType: formDateType,
      sort: 'ASC',
    });
    if (resData.code && resData.code === 200 && Array.isArray(resData.data)) {
      const selectedList = {};
      resData.data?.forEach((str, index) => {
        selectedList[str.name] = index === 0;
      });
      setListData([...resData.data]);
      setSelected(selectedList);
      if (columnList.length !== 1) {
        setOnlyOne(null);
      }
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
      setListData([]);
    }
    setLoading(false);
  };

  /**
   * @description 获取表头column数据
   */
  const getColData = async (list?: objType[]) => {
    setSelected({});
    const { deviceCode } = form.getFieldsValue();
    const dataList = list || deviceList;
    const equipId = dataList.find((str) => str.code === deviceCode)?.id;
    setLoading(true);
    const data = await queryMonitorIndex(equipId);
    if (data.code === 200 && data.data.length > 0) {
      const newList = (data.data as objType[]) || [];
      setColumnList([...newList]);
      if (newList.length === 1) {
        setOnlyOne(1);
      }
      queryListData(equipId);
    } else {
      setColumnList([]);
    }
    setLoading(false);
  };

  /**
   * @description 根据选择公司获取设备
   */
  const getDeviceData = async () => {
    const { monitorType, formCompany } = form.getFieldsValue();
    setListData([]);
    setColumnList([]);
    setLoading(true);
    const data = await queryEquipInfoByCompanyId({
      companyId: formCompany,
      type: monitorKey,
      monitorType,
      monitorOnline: '1',
    });
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      if (queryData?.equipCd) {
        form.setFieldsValue({ deviceCode: queryData.equipCd });
      } else {
        form.setFieldsValue({ deviceCode: data.data[0]?.code });
      }
      setDeviceList([...data.data]);
      getColData(data.data);
    } else {
      form.resetFields(['deviceCode']);
      setDeviceList([]);
    }
    setLoading(false);
  };

  const formatterNumber = (value: number) => {
    return value ? Math.trunc(value * 1000) / 1000 : '-';
  };

  // 遍历数组并计数true值的出现
  const onlyOneTrue = (arr: boolean[]): number | null => {
    const trueIndices = arr.map((value, index) => (value ? index : -1)).filter((i) => i !== -1);
    return trueIndices.length === 1 ? trueIndices[0] : null;
  };

  const option = useMemo(() => {
    const seriesList: object[] = [];
    columnList?.forEach((column) => {
      seriesList.push({
        name: column.name,
        type: magictype,
        data: listDatas.map((item) => item[column.code + chartType]),
        symbol: 'circle',
        symbolSize: 1,
        smooth: true,
      });
    });
    return {
      tooltip: {
        trigger: 'axis',
        formatter(params) {
          let result = `${params[0].name}<br>`; // 获取横轴对应的数据作为提示信息的标题
          // 先添加常规数据
          params.forEach((item) => {
            result += `${item.marker} ${item.seriesName}: ${formatterNumber(item.value)}`; // 对折线图数据进行格式化
            result += columnList[item.seriesIndex]?.measureUnit || '';
            result += `</div><div>`;
          });

          // 如果只选中一个指标，添加上下限值
          if (isNumber(onlyOne)) {
            const upperLimit = columnList[onlyOne]?.firstLevelMax;
            const lowerLimit = columnList[onlyOne]?.firstLevelMin;
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FF4500;"></span> 上限值: ${formatterNumber(upperLimit as number)}</div><div>`;
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#1E90FF;"></span> 下限值: ${formatterNumber(lowerLimit as number)}`;
          }

          return result;
        },
      },
      legend: {
        right: '6%',
        top: '-1%',
        data: columnList.map((str) => str.name),
        selected, // 默认不需要显示的设置为false
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      toolbox: {
        feature: {
          magicType: {
            type: ['line', 'bar'],
            title: {
              line: '切换为折线图',
              bar: '切换为柱状图',
            },
          },
          saveAsImage: {
            // 保存为图片
            show: true, // 是否显示该工具
            title: '保存',
          },
        },
        top: '3%',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: listDatas?.map((item) => item.TIME) || [],
      },
      yAxis: {
        type: 'value',
      },
      dataZoom: {
        startValue: 0,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        endValue: 15,
      },
      series: seriesList,
    };
  }, [chartType, selected, columnList, listDatas, onlyOne]);

  // 只在组件挂载时初始化一次图表
  useEffect(() => {
    if (chartRef.current) {
      // 初始化图表
      chartInstance.current = echarts.init(chartRef.current);

      // 设置事件监听
      chartInstance.current.on('legendselectchanged', (event: LegendSelectChangedEvent) => {
        const listData: boolean[] = Object.values(event.selected);
        setOnlyOne(onlyOneTrue(listData));
        setSelected(event.selected);
      });

      chartInstance.current.on('magictypechanged', (event) => {
        setMagictype(event.currentType);
      });
    }
    // 组件卸载时销毁图表
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []); // 空依赖数组，只执行一次

  // 单独的 effect 用于更新图表配置
  useEffect(() => {
    if (chartInstance.current) {
      // 根据新数据更新图表配置
      chartInstance.current.setOption(option, true);
    }
  }, [chartType, columnList, listDatas, onlyOne, selected]); // 只在这些值变化时更新配置

  useEffect(() => {
    if (monitorKey !== 'A22A08A01') {
      getMonitorList();
    }
  }, [monitorKey]);

  useEffect(() => {
    form.setFieldsValue({
      formCompany: queryData?.formCompany || [],
      formDateType: queryData?.dateType,
      deviceCode: queryData?.equipCd,
      monitorType: queryData?.monitorType,
      rangeDate: [moment(queryData?.startTm), moment(queryData?.endTm)],
    });
    setDateType(queryData?.dateType as DateListTypes['key']);
    getDeviceData();
  }, [queryData]);

  return (
    <div>
      <Form
        initialValues={{
          rangeDate: [moment().startOf('day'), moment()],
          formDateType: '1',
        }}
        form={form}
        name="GasLeakListNew"
        className={style['ant-gasleak-form']}
        onFinish={() => {
          queryListData();
        }}
      >
        <Row gutter={16} style={{ height: 50 }}>
          <Col span={6}>
            <Form.Item className={style['form-picker']} name="formCompany" label="所属单位">
              <Select
                placeholder="请选择"
                showSearch
                style={{ fontSize: 12 }}
                onSearch={() => {
                  getDeviceData();
                }}
                optionFilterProp="children"
                filterOption={(input: string, options?: { children: string; value: string }) =>
                  (options?.children ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onChange={() => {
                  getDeviceData();
                }}
              >
                {(companyList || []).map((item) => (
                  <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {monitorKey && monitorKey !== 'A22A08A01' && (
            <Col span={6}>
              <Form.Item name="monitorType" label="监测类型">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onChange={() => {
                    form.resetFields(['deviceCode']);
                    getDeviceData();
                  }}
                >
                  {(monitorList || []).map((item) => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          )}
          <Col span={6}>
            <Form.Item name="deviceCode" label="设备名称">
              <Select
                placeholder="请选择"
                showSearch
                style={{ fontSize: 12 }}
                onSearch={() => {
                  getColData();
                }}
                optionFilterProp="children"
                filterOption={(input: string, options?: { children: string; value: string }) =>
                  (options?.children ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onChange={() => {
                  getColData();
                }}
              >
                {deviceList.map((item) => (
                  <Select.Option key={item.code}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="formDateType" label="时间类型">
              <Select
                placeholder="请选择"
                style={{ fontSize: 12 }}
                onChange={(e) => {
                  setOverWeek(0);
                  setDateType(e);
                  switch (e) {
                    case '3':
                      form.setFieldsValue({
                        rangeDate: [
                          moment().subtract(7, 'days').startOf('day'),
                          moment().endOf('day'),
                        ],
                      });
                      break;
                    case '2':
                      form.setFieldsValue({
                        rangeDate: [moment().startOf('day'), moment().endOf('hour')],
                      });
                      break;
                    default:
                      form.setFieldsValue({ rangeDate: [moment().startOf('day'), moment()] });
                      break;
                  }
                }}
              >
                {dateList.map((item) => (
                  <Select.Option key={item.key}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16} style={{ height: 50 }}>
          <Col span={9}>
            <Form.Item name="rangeDate" label="监测时间">
              <RangePicker
                showTime
                format={
                  dateList.find((item) => item.key === dateType)?.format || 'YYYY-MM-DD HH:mm:ss'
                }
                onOk={(e) => {
                  const overDay = dateList.find((item) => item.key === dateType)?.over || 7;
                  if (e[1].diff(e[0], 'days') > overDay) {
                    message.warning(`监测时间范围请勿超过${overDay}天`);
                    setOverWeek(overDay);
                  } else {
                    setOverWeek(0);
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12} style={{ textAlign: 'end' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
              <Button
                style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                type="primary"
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                onClick={() => {
                  form.resetFields();
                  setDeviceList([]);
                  form.setFieldsValue({
                    formCompany: queryData?.formCompany || [],
                    formDateType: queryData?.dateType,
                    deviceCode: queryData?.equipCd,
                    monitorType: queryData?.monitorType,
                    rangeDate: [moment(queryData?.startTm), moment(queryData?.endTm)],
                  });
                  getDeviceData();
                }}
              >
                重置
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <Spin spinning={loading}>
        <div ref={chartRef} style={{ width: '100%', height: 500 }} />
      </Spin>
    </div>
  );
};
export default YTHLocalization.withLocal(EChartsComponent, locales, YTHLocalization.getLanguage());
