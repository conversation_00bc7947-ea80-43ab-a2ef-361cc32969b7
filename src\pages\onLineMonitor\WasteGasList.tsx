import React, { useState, useEffect, useRef } from 'react';
import type { IYTHColumnProps } from 'yth-ui/es/components/list/index';
import locales from '@/locales';

import {
  DatePicker,
  Form,
  Button,
  Table,
  Row,
  Col,
  message,
  Popover,
  Checkbox,
  Select,
  Modal,
  Empty,
} from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';
import { YTHLocalization } from 'yth-ui';
import moment from 'moment';
import { CurrentUser } from '@/Constant';
import formApi from '@/service/formApi';
import {
  queryUnitInfoByType,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  queryMonitorIndexValueItem,
} from '@/service/envApi';
import style from './index.module.less';
import ExportReportModl from './ExportReportModl';
import getTableScroll from './GetTableScroll';
import GasLeakCharts from './GasLeakCharts';

const { RangePicker } = DatePicker;

/** 时间类型types */
type DateTypes = 'hour' | 'daily' | 'realTime';

/** 时间list types */
type DateListTypes = {
  name: string;
  code: DateTypes;
  key: React.Key;
  format: string;
  over: 7 | 30;
};

type CacheTypes = {
  sourceType?: DateTypes;
  rangeDate?: moment.Moment;
};

// 时间类型
const dateList: DateListTypes[] = [
  { name: '时均值', code: 'hour', key: '2', format: 'YYYY-MM-DD HH', over: 7 },
  { name: '日均值', code: 'daily', key: '3', format: 'YYYY-MM-DD', over: 30 },
  { name: '实时数据', code: 'realTime', key: '1', format: 'YYYY-MM-DD HH:mm:ss', over: 7 },
];

const dataformat = [
  { value: '均值', key: 'avg' },
  { value: '累计值', key: 'sum' },
];

/**
 * 监测设备类型字典值
 */
const dictKey = 'A22A08A07';

/**
 * @description 实时监测 污染源监测
 * @returns
 */
const WasteGasList: React.FC = () => {
  const [form] = Form.useForm();
  const tableRef = useRef<HTMLDivElement>(null);
  const [columnList, setColumnList] = useState<IYTHColumnProps[]>([]); // 原始column表头数据
  const [filterCol, setFilterCol] = useState<IYTHColumnProps[]>([]); // 根据因子配置筛选的column数据
  const [listDatas, setListData] = useState<onLineMonitorTypes.objType[]>([]); // 接口返回数据
  const [checkValue, setCheckValue] = useState<React.Key[]>([]); // 因子配置选择的可见表头项
  const [loading, setLoading] = useState<boolean>(false);
  const [deviceList, setDeviceList] = useState<onLineMonitorTypes.objType[]>([]); // 根据选择的公司查询出的设备数据
  const [modalViseble, setModalViseble] = useState<boolean>(false); // 导出modal
  const [dateType, setDateType] = useState<DateTypes>('realTime');
  const [cacheData, setCacheData] = useState<CacheTypes>({ sourceType: 'realTime' }); // 缓存上一次选择的时间类型 防止更改时间类型导致其他功能受到影响
  const [scrollY, setScrollY] = useState<number | string>(0);
  const [chartType, setChartType] = useState<'avg' | 'sum'>('avg');
  const [chartsOpen, setChartsOpen] = useState(false);
  const [monitorList, setMonitorList] = useState<onLineMonitorTypes.objType[]>([]); // 监测类型数据
  const [queryData, setQueryData] = useState({}); // 查询参数 用于统计图数据
  const [companyList, setCompanyList] = useState<onLineMonitorTypes.objType[]>();
  const [overWeek, setOverWeek] = useState<number>(0); // 时间范围限制

  /**
   * 获取监测类型数据
   */
  const getMonitorList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: dictKey, // 监测类型 污染源
      },
      currentPage: 0,
      pageSize: 0,
    });
    const newList = list?.filter((item) => item.code !== 'A22A08A07A05') || [];
    setMonitorList([...newList]);
  };

  // 监测时间单独处理
  const fixedCol: IYTHColumnProps[] = [
    {
      code: 'collectTime',
      name: '监测时间',
      width: 180,
      title: '监测时间',
      align: 'center',
      dataIndex: 'collectTime',
      sorter: true,
      // sorter: (a, b) => {
      //   return Number(moment(a.TIME).unix()) - Number(moment(b.TIME).unix());
      // },
      render: (_r, record) => {
        return record.TIME || '-';
      },
      fixed: 'left',
      key: 'collectTime',
    },
  ];

  /**
   * @description 获取tableList数据
   */
  const queryListData = async (id?: string) => {
    const { rangeDate, formCompany, deviceCode, formDateType } = form.getFieldsValue();
    if (overWeek) {
      message.info(`监测时间不能超过${overWeek}天!!!`);
      return;
    }
    const equipId = id ?? deviceList.find((str) => str.code === deviceCode)?.id;
    setCacheData({ sourceType: formDateType, rangeDate });
    const data = {
      monitorType: 'wasteAir',
      equipCd: deviceCode,
      equipId,
      orgCd: formCompany || '',
      startTm: rangeDate[0].format('YYYY-MM-DD HH:mm:ss'),
      endTm: rangeDate[1].format('YYYY-MM-DD HH:mm:ss'),
      dateType: dateList.find((item) => item.code === formDateType)?.key,
    };
    setQueryData({
      ...data,
      formCompany,
      monitorType: form.getFieldValue('monitorType'),
    });
    setLoading(true);
    const resData = await queryMonitorIndexValueItem(data);
    if (resData.code && resData.code === 200 && resData.data) {
      setListData(Array.isArray(resData.data) ? [...resData.data] : []);
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
      setListData([]);
    }
    setLoading(false);
  };

  /**
   * @description 获取表头column数据 并更改结构
   */
  const getColData = async (list?: onLineMonitorTypes.objType[]) => {
    const { deviceCode } = form.getFieldsValue();
    const dataList = list || deviceList;
    const equipId = dataList.find((str) => str.code === deviceCode)?.id;
    setLoading(true);
    setColumnList([]);
    setCheckValue([]);
    const data = await queryMonitorIndex(equipId);
    if (data.code === 200 && data.data.length > 0) {
      const newList = (data?.data.map((item) => item.code) || []) as string[];
      const newDatas = data.data as IYTHColumnProps[];
      setColumnList([...newDatas]);
      setFilterCol([...fixedCol, ...newDatas]);
      setCheckValue([...newList]);
      queryListData(equipId);
      // const newList = data?.data.map((item) => item.code);
      // setColumnList([...data.data]);
      // setFilterCol([...fixedCol, ...data.data]);
      // setCheckValue([...newList]);
      // queryListData(equipId);
    } else {
      setFilterCol([...fixedCol]);
    }
    setLoading(false);
  };

  /**
   * @description 数据改成符合table columns数据
   * @param treeData
   * @returns
   */
  const dealTreeData = (treeData) => {
    const data = treeData.map((item, index) => {
      const newItem = {
        ...item,
        key: item.code + index,
        dataIndex: item.code + index,
        title: item.name,
        width: 180,
        align: 'center',
      };

      newItem.children = dataformat.map((str) => {
        return {
          key: item.code + str.key + index,
          dataIndex: item.code + str.key + index,
          align: 'center',
          width: 90,
          title: str.value,
          children: [
            {
              key: item.code + str.key,
              dataIndex: item.code + str.key,
              align: 'center',
              title: item.measureUnit,
              render: (_r, record) => {
                const newVal = item.code + str.key;
                if (record[newVal]) {
                  if (record.stat) {
                    return record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-';
                  }
                  if (
                    item.firstLevelMax &&
                    item.firstLevelMin &&
                    (record[newVal] > item.firstLevelMax || record[newVal] < item.firstLevelMin)
                  ) {
                    return (
                      <div className={style.triangle}>
                        {record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-'}
                      </div>
                    );
                  }
                  return record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-';
                }
                if (record[newVal] === 0) {
                  return '0';
                }
                return '-';
              },
            },
          ],
        };
      });
      if (!item.isTotalizeValue || item.isTotalizeValue === '0') {
        delete newItem.children[1];
        newItem.children[0].width = 180;
      }
      return newItem;
    });
    return data;
  };

  // 分页配置
  const paginationProps: TablePaginationConfig = {
    showSizeChanger: true,
    total: listDatas.length ?? 0,
    defaultPageSize: 20,
    showTotal: (total: number) => {
      return `共${total}条`;
    },
  };

  /**
   * @description 根据选择公司获取设备
   */
  const getDeviceData = async () => {
    const { monitorType, formCompany } = form.getFieldsValue();
    const unitCode = formCompany || CurrentUser().unitCode || '';
    setColumnList([]);
    setCheckValue([]);
    setListData([]);
    setFilterCol([...fixedCol]);
    setLoading(true);
    setQueryData({ formCompany, monitorType });
    const data = await queryEquipInfoByCompanyId({
      companyId: unitCode,
      type: dictKey,
      monitorType,
      monitorOnline: '1',
    });
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      setQueryData({
        formCompany,
        monitorType,
        deviceCode: data.data[0]?.code,
      });
      setDeviceList([...data.data]);
      form.setFieldsValue({ deviceCode: data.data[0]?.code });
      getColData(data.data);
    } else {
      form.resetFields(['deviceCode']);
      setDeviceList([]);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (checkValue.length === 0 || columnList.length === 0) return;
    const newList = [];
    columnList.forEach((item) => {
      checkValue.forEach((str) => {
        if (item.code === str) {
          newList.push(item);
        }
      });
    });
    // checkValue.forEach((item) => {
    //   columnList.forEach((str) => {
    //     if (item === str.code) {
    //       newList.push(str);
    //     }
    //   });
    // });
    setFilterCol([...fixedCol, ...dealTreeData(newList)]);
  }, [JSON.stringify(checkValue), columnList]);

  /**
   * 获取所属单位
   */
  const getCompanyList = async () => {
    const data = await queryUnitInfoByType(dictKey);
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      setCompanyList([...data.data]);
      form.setFieldsValue({ formCompany: data?.data[0]?.companyId || null });
      getDeviceData();
    }
  };

  /**
   * @description Popover弹出内容content
   */
  const popoverPontent: React.JSX.Element = (
    <div
      style={{
        width: 150,
        overflowY: 'auto',
      }}
    >
      {columnList.length === 0 ? (
        <Empty />
      ) : (
        <Checkbox.Group
          value={checkValue}
          // options={columnList.map((item) => {
          //   return { label: item.name, value: item.code };
          // })}
          onChange={(e) => {
            if (e.length === 0) return;
            setCheckValue(e as string[]);
          }}
        >
          <Row>
            {columnList.map((item) => {
              return (
                <Col key={item.code} span={24}>
                  <Checkbox value={item.code}>{item.name}</Checkbox>
                </Col>
              );
            })}
          </Row>
        </Checkbox.Group>
      )}
    </div>
  );

  // 页面加载完成后才能获取到对应的元素及其位置
  useEffect(() => {
    getMonitorList();
    getCompanyList();
    setScrollY(getTableScroll({ extraHeight: 144, ref: tableRef }));
  }, []);

  return (
    <div style={{ width: '100%', height: '100%', backgroundColor: '#DEE3E7' }}>
      <div style={{ padding: 8 }}>
        <Form
          initialValues={{
            rangeDate: [moment().startOf('day'), moment()],
            formDateType: dateType,
          }}
          form={form}
          name="GasLeakListNew"
          className={style['ant-gasleak-form']}
          onFinish={() => {
            queryListData();
          }}
        >
          <Row gutter={16} style={{ height: 50 }}>
            <Col span={6}>
              <Form.Item className={style['form-picker']} name="formCompany" label="所属单位">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getDeviceData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getDeviceData();
                  }}
                >
                  {(companyList || []).map((item) => (
                    <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="monitorType" label="监测类型">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  allowClear
                  onChange={() => {
                    getDeviceData();
                    form.resetFields(['deviceCode']);
                  }}
                >
                  {(monitorList || []).map((item) => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="deviceCode" label="设备名称">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getColData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getColData();
                  }}
                >
                  {(deviceList || []).map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="formDateType" label="时间类型">
                <Select
                  placeholder="请选择"
                  style={{ fontSize: 12 }}
                  onChange={(e: DateTypes) => {
                    setOverWeek(0);
                    setDateType(e);
                    switch (e) {
                      case 'daily':
                        form.setFieldsValue({
                          rangeDate: [
                            moment().subtract(7, 'days').startOf('day'),
                            moment().endOf('day'),
                          ],
                        });
                        break;
                      case 'hour':
                        form.setFieldsValue({
                          rangeDate: [moment().startOf('day'), moment().endOf('hour')],
                        });
                        break;
                      default:
                        form.setFieldsValue({ rangeDate: [moment().startOf('day'), moment()] });
                        break;
                    }
                  }}
                >
                  {dateList.map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16} style={{ height: 50 }}>
            <Col span={8} style={{ display: 'flex' }}>
              <Form.Item name="rangeDate" label="监测时间">
                <RangePicker
                  showTime
                  format={
                    dateList.find((item) => item.code === dateType)?.format || 'YYYY-MM-DD HH:mm:ss'
                  }
                  onOk={(e) => {
                    const overDay = dateList.find((item) => item.code === dateType)?.over || 7;
                    if (e[1].diff(e[0], 'days') > overDay) {
                      message.warning(`监测时间范围请勿超过${overDay}天`);
                      setOverWeek(overDay);
                    } else {
                      setOverWeek(0);
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={16} style={{ textAlign: 'end' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                <Button
                  style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                  type="primary"
                  htmlType="submit"
                >
                  查询
                </Button>
                <Button
                  style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                  onClick={() => {
                    form.resetFields();
                    form.setFieldsValue({
                      formDateType: 'realTime',
                      monitorType: null,
                      formCompany: companyList[0]?.companyId,
                    });
                    setDeviceList([]);
                    getDeviceData();
                    setDateType('realTime');
                    setCheckValue([]);
                  }}
                >
                  重置
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
        <div
          style={{
            padding: '10px 0',
            display: 'flex',
            justifyContent: 'end',
            backgroundColor: '#fff',
            height: 40,
          }}
        >
          <Button
            type="primary"
            size="small"
            style={{ marginLeft: 15, marginRight: 15 }}
            onClick={() => setModalViseble(true)}
          >
            报表
          </Button>
          <Button
            type="primary"
            size="small"
            onClick={() => setChartsOpen(true)}
            style={{ marginRight: 10 }}
            ghost
          >
            统计图
          </Button>
          <Popover placement="bottomRight" trigger="click" title={null} content={popoverPontent}>
            <Button type="primary" size="small" style={{ marginRight: 10 }} ghost>
              因子配置
            </Button>
          </Popover>
        </div>
        <Table
          loading={loading}
          bordered
          ref={tableRef}
          className={style['gas-table-list']}
          rowKey={(row) => row.id}
          dataSource={listDatas}
          rowClassName={(_, index) => {
            if (index % 2 === 0) {
              return style['even-row'];
            }
            return style['odd-row'];
          }}
          style={{ width: '100%', overflow: 'auto' }}
          columns={filterCol}
          pagination={paginationProps}
          scroll={{ x: checkValue.length * 180, y: scrollY }}
        />
        {/* 报表导出 */}
        <ExportReportModl
          open={modalViseble}
          columnsData={columnList}
          onResult={() => setModalViseble(false)}
          companyData={form.getFieldValue('formCompany')}
          deviceData={form.getFieldValue('deviceCode')}
          monitorType={form.getFieldValue('monitorType')}
          deviceList={deviceList}
          cacheData={cacheData}
          monitorKey={dictKey}
          companyList={companyList}
          envType="wasteAir"
          exportName="污染源监测"
        />

        {/* 统计图 */}
        <Modal
          width="80%"
          title={
            <div>
              <span>统计图</span>
              <Select
                defaultValue="均值"
                style={{ width: 100, marginLeft: 20 }}
                onChange={(e: 'avg' | 'sum') => setChartType(e)}
                options={[
                  {
                    value: 'avg',
                    label: '均值',
                  },
                  {
                    value: 'sum',
                    label: '累计值',
                  },
                ]}
              />
            </div>
          }
          visible={chartsOpen}
          onOk={() => {
            setChartsOpen(false);
          }}
          onCancel={() => {
            setChartsOpen(false);
          }}
          destroyOnClose
          footer={null}
        >
          {/* echarts */}
          <GasLeakCharts
            monitorKey={dictKey}
            chartType={chartType}
            envType="wasteAir"
            queryData={queryData}
            companyList={companyList}
          />
        </Modal>
      </div>
    </div>
  );
};
export default YTHLocalization.withLocal(WasteGasList, locales, YTHLocalization.getLanguage());
