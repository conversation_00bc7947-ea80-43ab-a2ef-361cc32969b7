# 溯源对象选择器

用于选择污染源企业及其溯源指标的组件。

## 使用方法

```tsx
import TraceObjectSelector from '@/components/common/TraceObjectSelector';

<TraceObjectSelector
  value={traceObjects}           // 已选择的对象ID数组
  onChange={setTraceObjects}     // 选择变化回调
  isTraceEnabled={true}          // 是否开启溯源
  disabled={false}               // 是否禁用
  type="edit"                    // 组件状态：view/edit/add
/>
```

## 基本逻辑

- **未开启溯源**: 显示 "-"
- **开启溯源**: 显示选择数量或"请选择"，点击打开表格选择器
- **懒加载**: 只有在打开弹窗时才调用接口获取数据，提升页面加载性能
- **数据缓存**: 数据获取后会缓存，再次打开不会重复请求
- **表格显示**: 公司名称 + 溯源指标两列，支持搜索和分页

## 属性说明

| 属性名 | 类型 | 说明 |
|--------|------|------|
| value | string[] | 已选择的对象ID数组 |
| onChange | (value: string[]) => void | 选择变化回调 |
| isTraceEnabled | boolean | 是否开启溯源功能 |
| disabled | boolean | 是否禁用 |
| type | 'view' \| 'edit' \| 'add' | 组件状态 |

## 数据格式

- **前端**: 数组格式 `["id1", "id2"]`
- **后端**: 字符串格式 `'["id1", "id2"]'`
- **自动转换**: 组件内部自动处理格式转换

## 数据量与性能

### 一次性加载方案
适合 **500-1000条** 数据（如50家公司 × 10+指标）

### 加载策略
- **一次性加载**: 获取所有数据，简单高效
- **本地分页**: Transfer组件内置分页，流畅体验
- **本地搜索**: 在已加载数据中快速搜索

### 性能优化配置
```typescript
// 一次性加载配置
fetchTraceData() {
  pageSize: 1000,         // 一次性获取所有数据
}

pagination={{
  pageSize: 50,           // 每页显示50条
  showSizeChanger: true,  // 允许用户调整页面大小
}}
```

## 注意事项

1. **溯源状态判断**: 只有当 `isTraceEnabled` 为 `true` 时才能选择溯源对象
2. **未开启溯源**: 显示 "-"，输入框禁用，不可点击
3. **数据加载策略**: 一次性加载所有数据，适合500条以内的数据量
4. 在查看模式下，输入框会被禁用
5. 穿梭框支持搜索功能，方便快速查找目标对象
6. `traceObjects` 字段会自动默认为空数组，无需手动初始化
7. 不需要单独存储 `traceObjectsCount` 字段，数量通过数组长度自动计算
8. 接口失败时会自动使用备用数据，确保功能可用
9. 数据格式会自动在数组和字符串之间转换，无需手动处理
10. 可以根据实际接口调整数据字段映射关系
