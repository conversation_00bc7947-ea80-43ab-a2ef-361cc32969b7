import * as React from 'react';
import { useWindowSize } from 'yth-ui/es/components/util/util';
import { YTHLocalization, YTHInput } from 'yth-ui';
import SubComponent from '@/test/components/sub';
import locales from '@/locales';

import style from './index.module.less';

interface ITestModuleProps {
  history: History;
}

const TestModule: React.FC<ITestModuleProps> = () => {
  const { isPC } = useWindowSize();
  const { t, setLocale } = YTHLocalization.useLocal();

  if (isPC) {
    return (
      <div>
        <div className={style['yth-test-moduel']}>
          <YTHInput value="1" onChange={() => {}} />
          <span className={style.aa}>我是PC</span>
          <span className={style.bb}>我是PC</span>
        </div>
        <div>国际化测试：{t('test.add')}</div>
        <SubComponent />
        <div>
          <button onClick={() => setLocale('zh-CN')}>切换中文</button>
          <button onClick={() => setLocale('en-US')}>切换英语</button>
          <button onClick={() => setLocale('pt-BR')}>切换葡萄牙语</button>
          <button onClick={() => setLocale('lo-LA')}>切换老挝语</button>
        </div>
        <hr />
        <p>环境变量：{process.env.YTH_TEST_VALUE}</p>
      </div>
    );
  }
  return <div className="yth-test-moduel">我是移动端</div>;
};

export default YTHLocalization.withLocal(TestModule, locales, YTHLocalization.getLanguage());
