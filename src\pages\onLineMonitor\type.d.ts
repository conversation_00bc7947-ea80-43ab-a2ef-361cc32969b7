// 自定义表头数据
declare namespace onLineMonitorTypes {
  // 每一项表头查询数据
  type colListType = {
    code: string;
    firstLevelMax: number;
    firstLevelMin: number;
    isTotalizeValue: boolean;
    measureUnit: string;
    name: string;
    number: number;
    secondLevelMax: number;
    secondLevelMin: number;
  };

  /**
   * @description 接口返回数据格式
   */
  type listResultType = {
    code?: number;
    current?: number;
    data?: colListType[];
    msg?: string;
    pageSize?: number;
    total?: number;
  };

  type objType = Record<string, string>;
}
