import React, { useState, useRef, Key } from 'react';
import { YTHList, YTHDialog, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message, Space } from 'antd';
import { getUuid } from '@/utils/customMethod';
import moment from 'moment';
import formApi from '@/service/formApi';
import type { ResponseType, ResponsPageType } from '@/service/envApi';
import locales from '@/locales';
import {
  queryUnitInfoByType,
  queryEquipInfoByCompanyId,
  queryManualFillTableList,
  deleteManualFillById,
} from '@/service/envApi';
import ManualFillDrawer from './ManualFillDrawer';
import ExportReportModl from './ExportReportModlNew';
import style from './fill.module.less';

/** 监测设备类型字典值 */
const dictKey: string = 'A22A08A06';

type objType = Record<string, string>;
type modalTypes = 'view' | 'add' | 'edit';
type queryDataType = {
  equipCd: objType[];
  monitorType: objType[];
  orgCd: objType[];
  createDate_start?: string;
  createDate_end?: string;
};

/**
 * @description 环境空气质量  手动填报 list页面
 * @returns
 */
const EnvQualityManualFillList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<modalTypes>('add'); // 是否是新增模式
  const [dataObj, setDataObj] = useState<{ [key: string]: Key }>({});
  // const [isCompanyAdmin, setIsCompanyAdmin] = useState<boolean>(false); // 标记是否是企业管理员
  const [modalViseble, setModalViseble] = useState<boolean>(false); // 导出modal
  const [queryData, setQueryData] = useState<queryDataType>(); // 查询条件
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  /**
   * 所属单位和监测类型变化后 重新查询设备数据
   */
  const getAccessList: () => Promise<void> = async () => {
    const monitorItem: objType[] = aa.getQuery().monitorType || [];
    const companyItem: objType[] = aa.getQuery().orgCd || [];
    aa.setQuery({ monitorType: monitorItem, orgCd: companyItem, equipCd: [] });
    if (companyItem) {
      aa.getQueryForm()
        .query('*')
        .forEach(
          (item: {
            path: { entire: string };
            setComponentProps: (props: unknown) => void;
            setValue: (value: unknown) => void;
          }) => {
            if (item.path.entire === 'equipCd') {
              item.setValue([{ text: null, code: null, id: null }]);
              item.setComponentProps({
                key: getUuid(16, 17),
                request: async () => {
                  const resData: ResponseType = await queryEquipInfoByCompanyId({
                    companyId: companyItem[0]?.code,
                    monitorType: monitorItem[0]?.code,
                    type: dictKey,
                    monitorOnline: '0',
                  });
                  if (resData?.data instanceof Array && resData?.data?.length > 0) {
                    const result: { text: string; code: string; id: string }[] = resData.data.map(
                      (strItem) => {
                        return { text: strItem.name, code: strItem.code, id: strItem.id };
                      },
                    );
                    return result ?? [];
                  }
                  return [];
                },
                searchable: true,
              });
            }
          },
        );
    }
  };

  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'orgCd',
      title: '所属单位',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { data } = await queryUnitInfoByType(dictKey);
          const plOp: { text: string; code: string }[] = [];
          if (data instanceof Array) {
            data.forEach((item: Record<string, string>) => {
              plOp.push({
                code: item.companyId,
                text: item.supplyUnit,
              });
            });
          }
          return plOp;
        },
        onChange: getAccessList,
        multiple: false,
        p_props: {
          placeholder: '请输入',
        },
      },
      render: (_v, record) => {
        return record.unitName || '-';
      },
    },
    {
      dataIndex: 'monitorType',
      title: '监测类型',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          placeholder: '请选择',
        },
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: dictKey,
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: { text: string; code: string }[] = [];
          list.forEach((item) => {
            plOp.push({
              text: item.text,
              code: item.code,
            });
          });
          return plOp;
        },
        onChange: getAccessList,
      },
      render: (_v, record) => {
        return record.monitorTypeText || '-';
      },
    },
    {
      dataIndex: 'equipCd',
      title: '设备名称',
      width: 180,
      query: true,
      display: false,
      componentName: 'Selector',
      componentProps: {
        multiple: false,
        p_props: {
          placeholder: '请选择',
        },
      },
    },
    {
      dataIndex: 'frequency',
      title: '采集频率',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          placeholder: '请选择',
        },
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A23A04',
            },
            currentPage: 0,
            pageSize: 0,
          });
          const plOp: object[] = [];
          list.forEach((item) => {
            plOp.push({
              text: item.text,
              code: item.remark,
            });
          });
          return plOp;
        },
        multiple: false,
      },
      render: (_v, record) => {
        return record.frequencyText || '-';
      },
    },
    {
      dataIndex: 'equipName',
      title: '设备名称',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'createdBy',
      title: '填报人',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'createDate',
      title: '填报时间',
      width: 180,
      queryMode: 'group',
      display: true,
      query: true,
      componentName: 'DatePicker',
      defaultValue: [
        moment().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
        moment().format('YYYY-MM-DD HH:mm:ss'),
      ],
      componentProps: {
        placeholder: '请输入',
        precision: `minute`,
        formatter: `YYYY-MM-DD HH:mm:ss`,
      },
    },
  ];

  const setModalTitle: () => string = () => {
    if (modalType === 'add') {
      return '新增';
    }
    if (modalType === 'view') {
      return '查看';
    }
    if (modalType === 'edit') {
      return '编辑';
    }
    return '';
  };

  // 关闭弹窗
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    aa.reload({});
    Modal.destroyAll();
  };

  const confirmDelete: (row: { id: string }) => Promise<void> = async (row) => {
    const res: ResponseType = await deleteManualFillById(row?.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
      aa.reload({});
    } else {
      message.error('删除数据出错');
    }
  };

  const deleteTemplateDialog: (row: { id: string }) => void = (row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  const handleFilter: (f: queryDataType) => Record<string, string> = (f) => {
    setQueryData(f);
    const filter: Record<string, string> = {};
    filter.type = dictKey;
    if (f.orgCd && f.orgCd.length > 0) {
      filter.unitCode = f.orgCd[0].code;
    }
    if (f.monitorType && f.monitorType.length > 0) {
      filter.monitorType = f.monitorType[0].code;
    }
    if (f.equipCd && f.equipCd.length > 0) {
      filter.equipCode = f.equipCd[0].code;
    }
    if (f.createDate_start && f.createDate_start !== '') {
      filter.startTm = f.createDate_start;
    }
    if (f.createDate_end && f.createDate_end !== '') {
      filter.endTm = f.createDate_end;
    }
    return filter;
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="envQualityManualFillList"
        action={aa}
        actionRef={ref}
        showRowSelection={false}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setEditMenuVisiable(true);
                    setModalType('add');
                    setDataObj({});
                  }}
                >
                  新增
                </Button>
              </div>
            ),
          },
        ]}
        listKey="id"
        extraOperation={[
          {
            element: (
              <Button size="small" onClick={() => setModalViseble(true)}>
                报表
              </Button>
            ),
          },
        ]}
        request={async (filter: queryDataType, pagination) => {
          const resData: ResponsPageType = await queryManualFillTableList({
            descs: [''],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            /*
             */
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          message.error('请求数据出错，请刷新重试或联系管理员');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={200}
        rowOperation={(row: { id: string; [key: string]: Key }) => {
          return [
            {
              element: (
                <div className={style['gas-leak-monitor-row-operator']}>
                  <Space size="middle">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('view');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      查看
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('edit');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      编辑
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        deleteTemplateDialog(row);
                      }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="60%"
        title={setModalTitle()}
        footer={null}
        visible={editMenuVisiable}
        onCancel={closeModal}
        destroyOnClose
        maskClosable={false}
      >
        <ManualFillDrawer
          type={modalType}
          closeModal={closeModal}
          dataObj={dataObj}
          queryData={queryData}
          dictKey="A22A08A06"
        />
      </Modal>

      {/* 报表导出 */}
      <ExportReportModl
        open={modalViseble}
        onResult={() => setModalViseble(false)}
        exportType="envQuality"
        monitorData={dictKey}
      />
    </div>
  );
};

export default YTHLocalization.withLocal(
  EnvQualityManualFillList,
  locales,
  YTHLocalization.getLanguage(),
);
