import React, { useState, useRef, useEffect } from 'react';
import { YTHLocalization, YTHList, YTHForm, YTHDialog } from 'yth-ui';
import { Button, Modal, Space, message } from 'antd';
import locales from '@/locales';
import dayjs from 'dayjs';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import analysisApi from '@/service/analysisApi';
/* eslint-disable import/no-extraneous-dependencies */
import { Form } from '@formily/core/esm/models';
import type { ApiResponse } from '@/service/analysisApi';
import style from './index.module.less';

// ===== 类型定义 =====

/**
 * 报告行数据接口
 * 用于表格行操作（删除、下载等）
 */
interface ReportRow {
  /** 报告ID */
  id: string;
  /** 报告文件路径（JSON字符串格式） */
  reportPath?: string;
}

/**
 * 过滤参数接口
 * 用于列表查询条件
 */
interface FilterParams {
  /** 报告月份 */
  reportMonth?: string;
  /** 其他动态过滤条件 */
  [key: string]: unknown;
}

/**
 * 分页参数接口
 * 用于列表分页
 */
interface PaginationParams {
  /** 当前页码 */
  current: number;
  /** 每页条数 */
  pageSize: number;
}

/**
 * 表单提交数据接口
 * 用于生成分析报告
 */
interface SubmitFormData {
  /** 报告时间 */
  reportTime: string;
  /** 报告名称 */
  reportName: string;
  /** 其他表单字段 */
  [key: string]: string | number;
}

/**
 * 列表请求返回数据接口
 */
interface ListRequestResult {
  data: Record<string, unknown>[];
  total: number;
  success: boolean;
}

/**
 * 分析报告列表组件
 * 功能：查看、生成、下载、删除分析报告
 * @returns JSX.Element
 */
const ReportList: React.FC = () => {
  // ===== 引用和状态管理 =====
  /** 列表操作引用，用于获取列表实例 */
  const listActionRef: React.MutableRefObject<ActionType> = useRef();
  /** 列表操作对象，用于刷新等操作 */
  const listAction: ActionType = YTHList.createAction();
  /** 表单实例，用于新增报告弹窗 */
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);
  /** 控制新增报告弹窗显示/隐藏 */
  const [visiable, setVisiable] = useState<boolean>(false);
  /** 控制提交按钮加载状态 */
  const [loading, setLoading] = useState<boolean>(false);

  // ===== 表格列配置 =====
  /**
   * 列表列配置
   * 定义表格显示的列、查询条件和格式化规则
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false, // 不在筛选中显示
    },
    {
      dataIndex: 'reportName',
      title: '报告名称',
      width: 180,
      query: true, // 支持查询
      display: true, // 在筛选中显示
    },
    {
      dataIndex: 'reportMonth',
      title: '报告月份',
      width: 180,
      query: true, // 支持查询
      display: true, // 在筛选中显示
      componentName: 'DatePicker', // 使用日期选择器
      componentProps: {
        precision: 'month', // 精确到月份
        formatter: 'YYYY-MM', // 格式化为年-月
        p_props: {
          placeholder: '请选择',
        },
      },
      // 格式化日期显示为 YYYY-MM 格式
      render: (value: string) => {
        return value ? dayjs(value).format('YYYY-MM') : '-';
      },
    },
    {
      dataIndex: 'userIdText',
      title: '操作人',
      width: 180,
      query: false, // 不支持查询
      display: true, // 在筛选中显示
    },
    {
      dataIndex: 'createDate',
      title: '生成时间',
      width: 180,
      query: false, // 不支持查询
      display: true, // 在筛选中显示
    },
  ];

  // ===== 业务方法 =====

  /**
   * 提交保存新增报告
   * 调用API生成分析报告，成功后关闭弹窗并刷新列表
   * @param data 表单提交数据，包含报告时间、名称等信息
   */
  const submitAddData: (data: SubmitFormData) => Promise<void> = async (data: SubmitFormData) => {
    setLoading(true); // 开启加载状态
    try {
      const res: { code?: number; message?: string } =
        await analysisApi.generateAnalysisReport(data);
      if (res && res.code && res.code === 200) {
        message.success('报告生成成功');
        setVisiable(false); // 关闭弹窗
        listAction.reload({}); // 刷新列表
      } else {
        message.error(res?.message || '报告生成失败');
      }
    } finally {
      setLoading(false); // 确保加载状态被关闭
    }
  };

  /**
   * 表单保存处理
   * 验证表单数据，转换时间格式后提交
   */
  const formSave: () => void = () => {
    form.validate().then(() => {
      // 获取表单值并进行类型断言
      const formValues: SubmitFormData = form.values as SubmitFormData;
      // 构造提交数据，将报告时间转换为月份的开始和结束时间
      const submitData: SubmitFormData = {
        ...formValues,
        startTm: dayjs(formValues.reportTime).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
        endTm: dayjs(formValues.reportTime).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      };
      submitAddData(submitData);
    });
  };

  /**
   * 确认删除数据
   * 调用删除API，成功后刷新列表
   * @param row 要删除的报告行数据
   */
  const confirmDelete: (row: ReportRow) => Promise<void> = async (row: ReportRow) => {
    const res: { code?: number } = await analysisApi.deleteReport(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除成功');
      listAction.reload({}); // 刷新列表
    }
  };

  /**
   * 处理查询过滤条件
   * 将 reportMonth 转换为时间范围（月份的开始和结束时间）
   * @param filter 原始过滤条件
   * @returns 处理后的过滤条件
   */
  const processFilterCondition: (filter: FilterParams) => FilterParams = (filter: FilterParams) => {
    const processedFilter: FilterParams = { ...filter };
    if (filter.reportMonth) {
      // 将月份转换为该月的开始和结束时间
      processedFilter.startTm = dayjs(filter.reportMonth)
        .startOf('month')
        .format('YYYY-MM-DD HH:mm:ss');
      processedFilter.endTm = dayjs(filter.reportMonth)
        .endOf('month')
        .format('YYYY-MM-DD HH:mm:ss');
      delete processedFilter.reportMonth; // 删除原始的 reportMonth 字段
    }
    return processedFilter;
  };

  /**
   * 下载报告文件
   * 解析报告路径，创建下载链接并触发下载
   * @param row 包含报告路径的行数据
   */
  const downloadReport: (row: ReportRow) => void = (row: ReportRow) => {
    if (row.reportPath) {
      // 解析JSON格式的文件路径
      const newPath: Array<{ url: string; fileName: string }> = JSON.parse(row.reportPath);
      const url: string = newPath?.[0]?.url;
      const fileName: string = newPath?.[0]?.fileName;
      const downloadUrl: string = `${window.location.origin}${url}`;

      // 创建临时的 a 标签来触发下载
      const link: HTMLAnchorElement = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName || '化工园区空气站月报'; // 使用文件名或默认名称
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.warning('报告文件路径不存在');
    }
  };

  // ===== 副作用处理 =====

  /**
   * 监听弹窗显示状态
   * 当弹窗打开时，初始化表单默认值
   */
  useEffect(() => {
    if (visiable) {
      form.setValues({
        reportTime: dayjs().format('YYYY-MM'), // 默认当前月份
        reportName: '', // 清空报告名称
      });
    }
  }, [visiable]);

  // ===== 渲染部分 =====

  return (
    <div style={{ width: '100%', height: '100%' }}>
      {/* 主列表组件 */}
      <YTHList
        defaultQuery={{}} // 默认查询条件
        code="analysisReportList" // 列表标识码
        action={listAction} // 列表操作对象
        actionRef={listActionRef} // 列表操作引用
        showRowSelection={false} // 不显示行选择
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setVisiable(true);
                  }}
                >
                  生成分析报告
                </Button>
              </div>
            ),
          },
        ]}
        extraOperation={[]}
        listKey="id"
        request={async (
          filter: FilterParams,
          pagination: PaginationParams,
        ): Promise<ListRequestResult> => {
          const resData: ApiResponse = await analysisApi.queryAnalysisReportList({
            descs: [''],
            condition: processFilterCondition(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            const dataWithSerialNo: Record<string, unknown>[] = (
              resData.data as Record<string, unknown>[]
            ).map((item: Record<string, unknown>, index: number) => ({
              ...item,
              serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
            }));
            return {
              data: dataWithSerialNo,
              total: resData.total || 0,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={100}
        rowOperation={(row: ReportRow) => {
          return [
            {
              element: (
                <div>
                  <Space size="small">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => downloadReport(row)}
                      style={{ fontSize: 12 }}
                    >
                      下载
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        YTHDialog.show({
                          type: 'confirm',
                          content: <p>确认删除此条数据？</p>,
                          onCancle: () => {},
                          onConfirm: () => {
                            confirmDelete(row);
                          },
                          p_props: {
                            cancelText: '取消',
                            okText: '确定',
                            title: '删除',
                          },
                        });
                      }}
                      style={{ fontSize: 12 }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="50%"
        title="生成分析报告"
        footer={null}
        visible={visiable}
        onCancel={() => setVisiable(false)}
        destroyOnClose
        maskClosable={false}
      >
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="id"
            title="id"
            labelType={1}
            required={false}
            display="hidden"
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="reportTime"
            title="月报时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              precision: 'month',
              formatter: 'YYYY-MM',
            }}
          />
          <YTHForm.Item
            name="reportName"
            title="报告名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{}}
          />
        </YTHForm>
        <div className={style['modal-footer']}>
          <Button onClick={() => setVisiable(false)}>取消</Button>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              formSave();
            }}
            style={{ marginLeft: 20 }}
          >
            生成
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(ReportList, locales, YTHLocalization.getLanguage());
